<route lang="json">
{
  "name": "database",
  "meta": {
    "title": "数据库列表",
    "requiresAuth": false,
    "sort": 1,
    "layout": "blank",
    "hideInMenu": true,
    "hideInBreadcrumb": true
  }
}
</route>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { Modal, message } from 'ant-design-vue'
import { DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import AiHeader from '../party-building/ai-header/index.vue'
import UploadModal from './components/UploadModal.vue'
import { deleteFile, getFileInfo } from '@/apis/manage/index'
import { download } from '@/utils/tools.js'

const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 20, // 每页中显示10条数据
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'], // 每页中显示的数据
  showTotal: total => `共有 ${total} 条数据`, // 分页中显示总的数据
})

// 控制上传弹框显示
const uploadModalVisible = ref(false)

// 处理模板下载并打开上传弹框
const handleTemplateDownload = () => {
  // 这里可以添加实际的模板下载逻辑
  uploadModalVisible.value = true
}

// 搜索表单数据
const searchForm = reactive({
  file_name: '',
  file_type: '',
  dateRange: [],
  upload_time: null,
})

// 表格列定义
const columns = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    customRender: ({ index }) => index + 1,
  },
  {
    title: '文件名称',
    dataIndex: 'file_name',
    key: 'file_name',
  },
  {
    title: '文件类型',
    dataIndex: 'file_type',
    key: 'file_type',
  },
  {
    title: '统计起止时间',
    dataIndex: 'start_time',
    key: 'start_time',
  },
  {
    title: '上传时间',
    dataIndex: 'upload_time',
    key: 'upload_time',
  },
  {
    title: '状态',
    dataIndex: 'is_parsed',
    key: 'is_parsed',
  },
  {
    title: '操作人',
    dataIndex: 'uploader',
    key: 'uploader',
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
  },
]

// 表格数据
const tableData = ref([])

// 获取状态对应的颜色
const getStatusColor = (status) => {
  const statusMap = {
    可用: 'success',
    解析中: 'warning',
    解析失败: 'error',
  }
  return statusMap[status] || 'default'
}
// const downloadTemplate = async () => {
//   download('/file/query_file', '模板.xlsx', {})
// }
const getList = async () => {
  let [start_time, end_time] = searchForm.dateRange
  start_time = start_time && dayjs(start_time).format('YYYY-MM-DD')
  end_time = end_time && dayjs(end_time).endOf('day').format('YYYY-MM-DD')
  const { items, page, page_size, total } = await getFileInfo({ page: pagination.current, page_size: pagination.pageSize, start_time, end_time, ...searchForm })
  tableData.value = items || []
  pagination.current = page
  pagination.pageSize = page_size
  pagination.total = total
}
const handleTableChange = (value) => {
  pagination.current = value.current
  pagination.pageSize = value.pageSize
  getList()
}

// 处理文件上传成功
const handleUploadSuccess = () => {
  getList()
}

// 处理删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '是否确认删除？',
    cancelText: '取消',
    okText: '确定',
    onOk: async () => {
      const res = await deleteFile({ file_id: record.file_id, file_type: record.file_type })
      if (res) {
        message.success('删除成功')
      }
      else {
        message.error('删除失败')
      }
      getList()
    },
  })
}
const onQuery = () => {
  getList()
}
onMounted(() => {
  getList()
})
</script>

<template>
  <div class="database-list">
    <AiHeader title="数据库列表" jump-path="party-building" />

    <div class="filter-wrapper">
      <div class="search-bar">
        <a-form layout="inline" :model="searchForm">
          <a-form-item>
            <a-input-group compact>
              <span class="label-text">文件名称：</span>
              <a-input v-model:value="searchForm.file_name" placeholder="请输入" @change="onQuery" />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-input-group compact>
              <span class="label-text">文件类型：</span>
              <a-select
                v-model:value="searchForm.file_type"
                placeholder="请选择"
                style="width: 200px"
                allow-clear
                @change="onQuery"
              >
                <a-select-option value="1">
                  CN2国际电力拥塞统计
                </a-select-option>
                <a-select-option value="2">
                  国际网络能力情况
                </a-select-option>
              </a-select>
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-input-group compact>
              <span class="label-text">统计起止时间：</span>
              <a-range-picker
                v-model:value="searchForm.dateRange"
                :placeholder="['开始日期', '结束日期']"
                allow-clear
                @change="onQuery"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-input-group compact>
              <span class="label-text">上传时间：</span>
              <a-date-picker
                v-model:value="searchForm.upload_time"
                placeholder="选择日期时间"
                allow-clear
                @change="onQuery"
              />
            </a-input-group>
          </a-form-item>
        </a-form>
      </div>
      <div class="action-buttons">
        <!--
        <a-button type="primary" ghost class="download-btn" @click="downloadTemplate">
          <template #icon>
            <DownloadOutlined />
          </template>
          模版下载
        </a-button>
        -->
        <a-button type="primary" @click="handleTemplateDownload">
          <template #icon>
            <PlusOutlined />
          </template>
          添加文件
        </a-button>
      </div>
    </div>
    <a-table :data-source="tableData" :columns="columns" :pagination="pagination" @change="handleTableChange">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'is_parsed'">
          <a-tag :color="getStatusColor(record.is_parsed ? '可用' : '解析失败')">
            {{ record.is_parsed ? '可用' : '解析失败' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'start_time'">
          {{ dayjs(record.start_time).format('YYYY-MM-DD ') }}~{{ dayjs(record.end_time).format('YYYY-MM-DD ') }}
        </template>
        <template v-if="column.key === 'upload_time'">
          {{ dayjs(record.upload_time).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-if="column.key === 'file_type'">
          <span v-if="record.file_type === '1'">
            CN2国际电力拥塞统计
          </span>
          <span v-if="record.file_type === '2'">
            国际网络能力情况
          </span>
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 使用上传弹窗组件 -->
    <UploadModal
      v-model:visible="uploadModalVisible"
      @upload="handleUploadSuccess"
    />
  </div>
</template>

<style lang="less" scoped>
.database-list {
  padding: 0 20px;
  background-image: url('./imgs/dbbackground.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  min-height: 100vh;

  .filter-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    background: #fff;
    padding: 20px;
    border-radius: 4px;

    .search-bar {
      flex: 1;
      margin-right: 20px;

      :deep(.ant-form-item) {
        margin-bottom: 0;
        margin-right: 12px;
      }

      :deep(.ant-input-group) {
        display: flex;
        align-items: center;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        height: 40px;
        padding: 0 11px;
        box-shadow: 0px 1px 8px 0px rgba(22,119,255,0.1);
        transition: all 0.3s ease;

        &:hover {
          border-color: #40a9ff;
          box-shadow:0px 2px 10px 0px rgba(22,119,255,0.2);
        }

        .label-text {
          color: #606D86;
          margin-right: 8px;
          white-space: nowrap;
          line-height: 30px;
        }

        .ant-select {
          .ant-select-arrow {
            color: #1677FF;
          }
        }

        .ant-picker {
          .ant-picker-suffix {
            color: #1677FF;
          }
        }

        .ant-input {
          width: 180px;
        }

        .ant-select {
          width: 200px;
        }

        .ant-range-picker {
          width: 240px;
        }

        .ant-picker {
          width: 180px;
        }

        .ant-input,
        .ant-select-selector,
        .ant-picker {
          background: transparent;
          border: none !important;
          padding: 0 !important;
          height: 30px !important;
          line-height: 30px !important;
          box-shadow: none !important;

          &:focus {
            box-shadow: none !important;
          }
        }

        .ant-select-selector {
          padding-right: 24px !important;
        }

        .ant-picker-input {
          height: 30px;

          input {
            height: 30px;
            line-height: 30px;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      align-items: center;
      white-space: nowrap;

      .ant-btn-primary {
        &:not(.download-btn) {
          background: linear-gradient( 304deg, #3D39FF 0%, #5E70FF 41%, #808EF6 100%);
          border: none;

          &:hover {
            background: linear-gradient( 304deg, #3D39FF 0%, #5E70FF 41%, #808EF6 100%);
            opacity: 0.9;
          }
        }
      }
      .download-btn {
        margin-right: 12px;
        border: 1px solid transparent;
        background:
        linear-gradient(white, white) padding-box, /* 内容背景 */
        linear-gradient(222deg, rgba(189, 79, 255, 1), rgba(79, 82, 255, 1), rgba(51, 156, 255, 1))  border-box; /* 边框背景 */
      }
      .anticon {
        font-size: 16px;
      }
    }
  }

  :deep(.ant-btn) {
    border-radius: 4px;
    height: 32px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .anticon {
      margin-right: 6px;
    }
  }

  :deep(.ant-table) {
    .ant-table-thead {
      tr:first-child {
        th:first-child {
          border-top-left-radius: 8px;
        }
        th:last-child {
          border-top-right-radius: 8px;
        }
      }

      > tr > th {
        background: #F5FAFF !important;
        border-bottom: 1px solid rgba(96, 109, 134, 0.25);
        padding: 16px;
        font-weight: 500;
        color: #333;
        text-align: center !important;

        &::before {
          display: none;
        }
      }
    }

    .ant-table-container {
      border: 1px solid rgba(96, 109, 134, 0.25);
      border-start-start-radius: 8px;
      border-start-end-radius: 8px;
      overflow: hidden;

      table {
        border-collapse: collapse;
      }
    }

    .ant-table-tbody {
      > tr {
        > td {
          border-bottom: 1px solid rgba(96, 109, 134, 0.25);
          text-align: center;
        }

        &:last-child > td {
          border-bottom: none;
        }
      }
    }

    // 移除竖向边框
    .ant-table-cell {
      border-right: none;
    }

    // 操作列居中对齐
    .ant-table-cell .ant-space {
      justify-content: center;
    }
  }
}

.upload-hint {
  color: #999;
  font-size: 12px;
  margin-top: 8px;
}
</style>

