.__GPT__bubble-wrap {
  height: 80px;
  width: 80px;
  border-radius: 40px;
  background-position: center;
  background-size: cover;
  position: fixed;
  top: 30px;
  right: 20px;
  z-index: 999999;
  transition: opacity 0.3s;
  cursor: pointer;
}
.__GPT__bubble-wrap.__GPT__hide {
  opacity: 0;
  pointer-events: none;
}
.__GPT__bubble-wrap:active {
  cursor: grabbing;
}
.__GPT__tooltip {
  position: relative;
  display: inline-block;
}
.__GPT__tooltip::before {
  content: "点我AI问答哦！";
  position: absolute;
  top: -26px;
  right: -40px;
  width: 115px;
  height: 24px;
  padding: 0 8px;
  border: 1px solid #5AC2f0;
  border-radius: 4px 14px 0 4px;
  background-color: #5AC2f0;
}
.__GPT__chat-wrap {
  height: 100vh;
  width: 50vw;
  min-width: 600px;
  background-color: #ddd;
  border: none;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 99999;
  transform: translateX(100%);
  transition: transform 0.3s;
}
@media (max-width: 1400px) {
  .__GPT__chat-wrap {
    height: 100vh;
    width: 60vw;
  }
}
.__GPT__chat-wrap.__GPT__show {
  transform: translateX(0);
}
.__GPT__chat-mask {
  position: fixed;
  z-index: 99999;
  width: 0;
}
.__GPT__chat-mask::after {
  content: '';
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  transition: opacity 0.3s;
}
.__GPT__chat-mask.__GPT__show::after {
  display: block;
}
.__GPT__chat-mask.__GPT__show {
  width: 100%;
}
