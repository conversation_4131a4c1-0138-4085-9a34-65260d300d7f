{"root": true, "env": {"node": true, "browser": true, "es6": true}, "extends": ["plugin:vue-scoped-css/vue3-recommended", "@antfu"], "plugins": ["check-file"], "rules": {"curly": ["error", "all"], "check-file/folder-naming-convention": ["error", {"src/**/": "KEBAB_CASE"}], "check-file/filename-naming-convention": ["error", {"src/!(components)/*": "+([a-z])*([a-z0-9])*(-+([a-z0-9]))|_+([a-z])*([a-z0-9])*(-+([a-z0-9]))", "src/**/!(components)/*": "+([a-z])*([a-z0-9])*(-+([a-z0-9]))|_+([a-z])*([a-z0-9])*(-+([a-z0-9]))", "src/components/**/*.vue": "*([A-Z]*([a-z0-9]))|*_+([A-Z]*([a-z0-9]))", "src/**/components/**/*.vue": "*([A-Z]*([a-z0-9]))|*_+([A-Z]*([a-z0-9]))"}]}}