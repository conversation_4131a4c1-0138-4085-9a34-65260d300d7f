// 你可以在此写全局样式

// 全局主题定制，具体参考 https://antdv.com/docs/vue/customize-theme-variable-cn
:root {
  --ant-primary-color: #4F52FF;
  --ant-primary-color-hover: #4F52FF;
  // --ant-primary-color-active: #096dd9;
  // --ant-primary-color-outline: rgba(24, 144, 255, 0.2);
  // --ant-primary-1: #e6f7ff;
  // --ant-primary-2: #bae7ff;
  // --ant-primary-3: #91d5ff;
  // --ant-primary-4: #69c0ff;
  // --ant-primary-5: #40a9ff;
  // --ant-primary-6: #1890ff;
  // --ant-primary-7: #096dd9;
  // --ant-primary-color-deprecated-pure: ;
  // --ant-primary-color-deprecated-l-35: #cbe6ff;
  // --ant-primary-color-deprecated-l-20: #7ec1ff;
  // --ant-primary-color-deprecated-t-20: #46a6ff;
  // --ant-primary-color-deprecated-t-50: #8cc8ff;
  // --ant-primary-color-deprecated-f-12: rgba(24, 144, 255, 0.12);
  // --ant-primary-color-active-deprecated-f-30: rgba(230, 247, 255, 0.3);
  // --ant-primary-color-active-deprecated-d-02: #dcf4ff;
  // --ant-success-color: #52c41a;
  // --ant-success-color-hover: #73d13d;
  // --ant-success-color-active: #389e0d;
  // --ant-success-color-outline: rgba(82, 196, 26, 0.2);
  // --ant-success-color-deprecated-bg: #f6ffed;
  // --ant-success-color-deprecated-border: #b7eb8f;
  // --ant-error-color: #ff4d4f;
  // --ant-error-color-hover: #ff7875;
  // --ant-error-color-active: #d9363e;
  // --ant-error-color-outline: rgba(255, 77, 79, 0.2);
  // --ant-error-color-deprecated-bg: #fff2f0;
  // --ant-error-color-deprecated-border: #ffccc7;
  // --ant-warning-color: #faad14;
  // --ant-warning-color-hover: #ffc53d;
  // --ant-warning-color-active: #d48806;
  // --ant-warning-color-outline: rgba(250, 173, 20, 0.2);
  // --ant-warning-color-deprecated-bg: #fffbe6;
  // --ant-warning-color-deprecated-border: #ffe58f;
  // --ant-info-color: #1890ff;
  // --ant-info-color-deprecated-bg: #e6f7ff;
  // --ant-info-color-deprecated-border: #91d5ff;
}

// 登陆背景图片
.castle-login-wrap-bg {
  isolation: isolate;
  position: relative;
  overflow: hidden;

  &::after {
    position: absolute;
    content: "";
    background-color: #00bbf9;
    border-radius: 40% 20% 20% 30%;
    opacity: 0.7;
    height: 50vw;
    width: 50vw;
    filter: blur(calc(40vw / 10));
    margin: auto;
    top: 40vh;
    left: 50vw;
    z-index: -1;
    animation: transform 30s ease-in-out infinite both alternate,
      movement_one 40s ease-in-out infinite both;
  }

  &::before {
    position: absolute;
    content: "";
    background-color: #F15BB5;
    border-radius: 30% 50% 20% 40%;
    opacity: 0.7;
    height: 30vw;
    width: 30vw;
    filter: blur(calc(40vw / 10));
    margin: auto;
    top: 10vh;
    left: 70vw;
    z-index: -1;
    animation: transform 20s ease-in-out infinite both alternate,
      movement_one 40s ease-in-out infinite both;
  }

  @keyframes transform {
    0%, 100% { border-radius: 33% 67% 70% 30% / 30% 30% 70% 70% }
    20% { border-radius: 37% 63% 51% 49% / 37% 65% 35% 63% }
    40% { border-radius: 36% 64% 64% 36% / 64% 48% 52% 36% }
    60% { border-radius: 37% 63% 51% 49% / 30% 30% 70% 70% }
    80% { border-radius: 40% 60% 42% 58% / 41% 51% 49% 59% }
  }
  @keyframes movement_one {
    0%, 100% { transform: none }
    50% { transform: rotateY(10deg) scale(1.2) }
  }
}

.ant-pro-page-container-children-content {
  margin: 12px 12px 0 12px;
}
.ant-pro-grid-content-children {
  padding-bottom: 12px;
}

.pro-list {
  .ant-form {
    .ant-form-item {
      margin-bottom: 0px !important;
    }
  }
}

.max-w-screen-md-pro {
  width: 60vw !important;
}
