<route lang="json">
{
  "meta": {
    "title": "华信GPT",
    "icon": "DesktopOutlined",
    "requiresAuth": false,
    "sort": 1,
    "layout": "blank",
    "hideInMenu": true,
    "hideInBreadcrumb": true
  }
}
</route>

<script setup>
import { message } from 'ant-design-vue'
import { computed, onBeforeMount, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue'
import VueMarkdown from 'vue-markdown-render'
import dayjs from 'dayjs'
import { Howl } from 'howler'
import RobotIcon from '../images/robot.png'
import RobotHello from '../images/robot-hello.mp4'
import RobotThink from '../images/robot-think.mp4'
import './../theme.less'
import {
  categoryList,
  chatBoxWrap,
  chatList,
  chatLoading,
  contentStr,
  creatLoading,
  dislike,
  getCategoryList,
  getHistoryChatListDetail,
  getOuterSrcParam,
  gptTips,
  handleChangeStatus,
  handleClearChatList,
  handleEvaluate,
  handleInputChange,
  handleScrolled,
  isAnswer,
  like,
  outerSrcParams,
  preventEnter,
  replyLoading,
  robotImage,
  scrollChatBoxToBottom,
  selectCategory,
  sendCategoryChangeMsg,
  session_id,
  setSessionId,
  showToBottom,
  similarQuestions,
  userHandleChat,
  userHandleFirstChat,
  userHandlePause,
  userInputMessage,
} from './chat'
import { createNewConversation } from '@/apis/manage/index'
import TypeWriter from '@/components/TypeWriter.vue'
import audioSrc from '@/assets/audio/robot-hello.mp3'
const props = defineProps({
  category: {
    type: Object,
    default: () => ({}),
  },

})

const emit = defineEmits(['changeCategory', 'chatRefresh', 'creatLoading'])
const videoPlayer = ref(null)
const userInputMessageOffline = ref('')
const state = reactive({
  isInputFocused: false,
  curEvaluateType: null,
  isRecording: false,
  audioContext: null,
  audioStream: null,
  ws: null,
  processor: null,
  connecting: false,
  recognitionResult: '',
  error: '',
  sampleRate: 16000, // 采样率
  bufferSize: 4096, // 缓冲区大小
  silenceThreshold: 0.01, // 静音阈值
  silenceCounter: 0,
  isSpeaking: false,
})

const isVideoEnded = ref(false)

const sound = ref(null)
const isPlaying = ref(false)
const showUnmutePrompt = ref(false)
const showPlayButton = ref(false)
const playFailed = ref(false)
const canPlay = ref(true)
const vidioPlayTime = ref(0)
// 初始化音频
const initAudio = () => {
  sound.value = new Howl({
    src: [audioSrc],
    volume: 0, // 初始静音
    autoplay: true, // 尝试自动播放
    onplay() {
      console.log('开始播放')
      isPlaying.value = true
      sound.value.volume(1.0)
      showUnmutePrompt.value = false
    },
    onend() {
      isPlaying.value = false
    },
    onloaderror: (id, err) => {
      console.error('加载错误:', err)
    },
    onplayerror() {
      console.log('播放失败')
      playFailed.value = true
      showPlayButton.value = true // 显示播放按钮
    },
  })
}

// 手动播放
const playAudio = () => {
  console.log('播放音频', canPlay.value, sound.value)

  if (!canPlay.value) {
    console.log('8秒内只能播放一次')
    return
  }
  if (sound.value) {
    canPlay.value = false
    sound.value.play()
    sound.value.volume(1.0)
    showPlayButton.value = false
    isPlaying.value = true
  }
  // 8秒后重置播放权限
  setTimeout(() => {
    canPlay.value = true
  }, 8000)
}
const handleVideoEnd = () => {
  vidioPlayTime.value = vidioPlayTime.value + 1

  if (vidioPlayTime.value >= 2) {
    isVideoEnded.value = true
  }
  else {
    videoPlayer.value.play()
  }
}
const currentCategory = computed(() => {
  return props.category
})

const isMobileDevice = computed(() => {
  return window.innerWidth <= 768
})

const handleScroll = () => {
  handleScrolled.value = true
  showToBottom.value = chatBoxWrap.value?.scrollTop + chatBoxWrap.value?.clientHeight + 10 < chatBoxWrap.value?.scrollHeight
}
const handleSelectCategory = async (item) => {
  emit('changeCategory', item)
  await selectCategory(item)
  sendCategoryChangeMsg()
}
const handleClearChatInput = () => {
  userInputMessage.value = undefined
}

const copyQuestionToInput = (question) => {
  userInputMessage.value = question
}
const showComment = (type, index, item) => {
  state.curEvaluateType = type
  item.commentAreaVisible = true
}
const submitComment = async (index, item) => {
  await handleEvaluate(state.curEvaluateType, index, item)
  item.commentAreaVisible = false
}

onBeforeMount(() => {
  getCategoryList()
})

onMounted(async () => {
  initAudio()
  // 获取url参数
  getOuterSrcParam()
  document.title = '问数机器人'
  if (!chatList.value.find(item => !item.isHead)) {
    chatList.value.push()
  }
})

const currnetClassName = computed(() => {
  let className = 'SGCB-layout'
  if (outerSrcParams.themeName) {
    className = `${outerSrcParams.themeName}-layout`
  }
  return `layout ${className}`
})

const isLastChatReply = (item, index) => {
  return !item.isMe && index !== 0 && index === chatList.value.length - 1
}
const userHandleSend = async (e) => {
  if (!session_id.value) {
    const res = await createNewConversation()
    const sessionIdList = JSON.parse(localStorage.getItem('session_ids')) || []
    sessionIdList.push(res.session_id)
    window.localStorage.setItem('session_ids', JSON.stringify(sessionIdList))
    setSessionId(res.session_id)
    await userHandleFirstChat(e)
  }
  else {
    await userHandleChat(e)
  }
}

watch(creatLoading, (val) => {
  if (!val) {
    emit('chatRefresh')
  }
  emit('creatLoading', val)
})

const stopRecording = () => {
  if (state.audioStream) {
    state.audioStream.getTracks().forEach(track => track.stop())
  }

  if (state.processor) {
    state.processor.disconnect()
  }

  if (state.audioContext) {
    state.audioContext.close()
  }

  // 发送结束标志
  if (state.ws && state.ws.readyState === WebSocket.OPEN) {
    const endMsg = JSON.stringify({
      chunk_interval: 10,
      chunk_size: [5, 10, 5],
      mode: '2pass',
      wav_name: 'h5',
      is_speaking: false,
    })
    state.ws.send(endMsg)
    setTimeout(() => {
      state.ws.close()
      state.ws = null
    }, 1000)
  }

  state.isRecording = false
  state.audioStream = null
  state.audioContext = null
  state.processor = null
  message.info('录音已停止')
}
const initWebSocket = () => {
  if (state.ws) {
    state.ws.close()
  }

  state.ws = new WebSocket('ws://10.10.7.74:10095')

  state.ws.onopen = () => {
    userInputMessage.value = ''
    userInputMessageOffline.value = ''
    const initMsg = {
      mode: '2pass',
      chunk_size: [5, 10, 5],
      hotwords: '{"阿里巴巴":20,"hello world":40}',
      is_speaking: true,
      chunk_interval: 10,
      wav_name: 'h5',
      itn: false,
      // 添加更多优化参数
      sample_rate: state.sampleRate,
      beam_size: 10,
      max_active_paths: 4,
      temperature: 0.8,
      language: 'zh',
      model_type: 'large',
    }
    state.ws.send(JSON.stringify(initMsg))
  }

  state.ws.onmessage = (event) => {
    try {
      const response = JSON.parse(event.data)
      if (response.text && response.mode === '2pass-offline') {
        userInputMessageOffline.value += response.text.replace(/<\|(en|zh|yue|jp|ko|NEUTRAL|EMO_UNKNOWN|HAPPY|SAD|ANGRY|Speech)\|>|(\|withitn\|>|<\|woitn\|>)/g, '')
        userInputMessage.value = userInputMessageOffline.value
      }
      if (response.text && response.mode === '2pass-online') {
        userInputMessage.value += response.text.replace(/<\|(en|zh|yue|jp|ko|NEUTRAL|EMO_UNKNOWN|HAPPY|SAD|ANGRY|Speech)\|>|(\|withitn\|>|<\|woitn\|>)/g, '')
      }
    }
    catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  state.ws.onerror = (error) => {
    console.error('WebSocket错误:', error)
    stopRecording()
  }

  state.ws.onclose = () => {
    stopRecording()
  }
}

// 检测是否有声音
const detectVoice = (audioData) => {
  let sum = 0
  for (let i = 0; i < audioData.length; i++) {
    sum += Math.abs(audioData[i])
  }
  const average = sum / audioData.length
  return average > state.silenceThreshold
}

// 音频预处理
const preprocessAudio = (audioData) => {
  // 1. 归一化
  let max = 0
  for (let i = 0; i < audioData.length; i++) {
    max = Math.max(max, Math.abs(audioData[i]))
  }
  if (max > 0) {
    for (let i = 0; i < audioData.length; i++) {
      audioData[i] = audioData[i] / max
    }
  }

  // 2. 降噪（简单实现）
  const windowSize = 3
  const smoothedData = new Float32Array(audioData.length)
  for (let i = 0; i < audioData.length; i++) {
    let sum = 0
    let count = 0
    for (let j = Math.max(0, i - windowSize); j <= Math.min(audioData.length - 1, i + windowSize); j++) {
      sum += audioData[j]
      count++
    }
    smoothedData[i] = sum / count
  }

  return smoothedData
}

const processAudio = (e) => {
  if (state.isRecording && state.ws && state.ws.readyState === WebSocket.OPEN) {
    const inputData = e.inputBuffer.getChannelData(0)

    // 检测是否有声音
    const hasVoice = detectVoice(inputData)
    if (hasVoice) {
      state.silenceCounter = 0
      state.isSpeaking = true
    }
    else {
      state.silenceCounter++
      if (state.silenceCounter > 10) { // 连续10帧静音
        state.isSpeaking = false
      }
    }

    // 只在检测到声音时发送数据
    if (state.isSpeaking) {
      // 预处理音频
      const processedData = preprocessAudio(inputData)

      // 将Float32Array转换为Int16Array
      const pcmData = new Int16Array(processedData.length)
      for (let i = 0; i < processedData.length; i++) {
        pcmData[i] = Math.max(-32768, Math.min(32767, Math.round(processedData[i] * 32768)))
      }

      // 发送音频数据
      state.ws.send(pcmData.buffer)
    }
  }
}

const startRecording = async () => {
  try {
    state.connecting = true
    await initWebSocket()

    // 获取麦克风权限，优化音频参数
    state.audioStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        channelCount: 1,
        sampleRate: state.sampleRate,
        sampleSize: 16,
      },
    })

    state.audioContext = new (window.AudioContext || window.webkitAudioContext)({
      sampleRate: state.sampleRate,
      latencyHint: 'interactive',
    })

    const source = state.audioContext.createMediaStreamSource(state.audioStream)

    // 创建脚本处理器处理音频数据
    state.processor = state.audioContext.createScriptProcessor(
      state.bufferSize,
      1,
      1,
    )
    state.processor.onaudioprocess = processAudio

    source.connect(state.processor)
    state.processor.connect(state.audioContext.destination)

    state.isRecording = true
    state.recognitionResult = ''
    state.error = ''
    state.connecting = false
    state.silenceCounter = 0
    state.isSpeaking = false
    message.success('开始录音')
  }
  catch (error) {
    state.error = `录音启动失败: ${error.message}`
    state.connecting = false
    message.error('无法访问麦克风')
    console.error(error)
  }
}

const handleVoiceInput = async () => {
  if (!state.isRecording) {
    await startRecording()
  }
  else {
    stopRecording()
  }
}

const scrollToBottom = () => {
  handleScrolled.value = false
  scrollChatBoxToBottom()
}
// 组件卸载时清理资源
onBeforeUnmount(() => {
  stopRecording()
})

defineExpose({
  handleSelectCategory,
  handleClearChatList,
  setSessionId,
  getHistoryChatListDetail,
  scrollChatBoxToBottom,
})
</script>

<template>
  <div class="bk-wrapper">
    <div class="flex flex-col items-center h-full p-1 pr-0">
      <div id="chatBox" ref="chatBoxWrap" class="flex justify-center flex-grow w-full overflow-y-auto chatBoxWrap relative" @scroll="handleScroll">
        <div class="w-full max-w-screen-md-pro pr-0">
          <ul class="p-0 m-0 list-none">
            <div v-if="chatList.length > 1" class="pt-0.5  flex flex-col  w-full max-w-screen-md-pro text-center mb-3 fixed -top-10">
              <span style="font-size: 16px">{{ chatList[0]?.content || 问数机器人 }}</span>
            </div>
            <div v-if="chatList.length <= 1" class="flex flex-col items-center w-full">
              <div class="video-container" :class="{ 'video-ended': isVideoEnded }" @click="playAudio">
                <img class="w-24 video-thumbnail" :src="RobotIcon">
                <video
                  ref="videoPlayer"
                  class="video-player"
                  autoplay
                  muted
                  playsinline
                  @ended="handleVideoEnd"
                >
                  <source :src="RobotHello" type="video/mp4">
                </video>
              </div>

              <span class="pt-4 pb-4  mt-4 font-bold  robot-name">
                <span class="gradient-text"><TypeWriter text="您好，我是问数机器人" :need-play-audio="true" /></span>
              </span>
              <div class="text-[#606D86] mt-4 xin-bao-description">
                <TypeWriter text="我拥有全域数据分析能力，问答即洞察，让粮食数据开口说话~" />
              </div>
            </div>
            <li v-for="(item, index) in chatList" :id="`answer-anhor-${index}`" :key="item.content" class="p-1" :class="{ 'chat-right': item.isMe }">
              <div class="flex items-start w-full">
                <div v-if="item.isMe" class="mt-1 w-[52px] h-[52px] min-w-[52px] avatar-icon" />
                <div v-else class="circular-video-container" :class="{ 'circular-video-ended': !chatLoading || !isLastChatReply(item, index) }">
                  <video autoplay loop muted playsinline class="circular-video">
                    <source :src="RobotThink" type="video/mp4">
                  </video>
                  <img class="circular-video-thumbnail " :src="RobotIcon">
                </div>
                <div class="px-2 chat-wrap">
                  <p class="mb-1 text-gray-300">
                    {{ dayjs(item.time).format(' YYYY-MM-DD HH:mm:ss') }}
                  </p>
                  <div v-if="!item.isMe">
                    <div class="text-[#000000] bg-[#00000020] rounded-md p-1 w-fit flex justify-center items-center cursor-pointer  text-[12px]" @click="handleChangeStatus(item, index)">
                      <img src="../images/star-think.png" alt="">
                      <span v-if="isLastChatReply(item, index) && contentStr && chatLoading" class="ml-2 mr-2">思考中...</span>
                      <span v-else>
                        <span class="ml-2 mr-2">{{ item.isShow ? '收起思考' : '展开思考' }}</span>
                        <DownOutlined :class="{ rotated: item.isShow }" />
                      </span>
                    </div>
                  </div>
                  <div :class="{ 'chat-overflow': chatLoading && isLastChatReply(item, index) }">
                    <div class="pl-4 pr-4 pt-1 pb-1 rounded-3xl chat-pop" :class="{ 'my-chat-box, my-answer-box': item.isMe, 'robot-chat-box': !item.isMe }">
                      <VueMarkdown v-if="item.questionAbstract" class="markdown-wrap-problem" :options="{ html: true }" :source="gptTips.guessText + item.questionAbstract || ''" />
                      <VueMarkdown :id="`markdown-wrap-${index}`" class="markdown-wrap" :options="{ html: true }" :source="isLastChatReply(item, index) && contentStr ? contentStr : item.content" />
                      <div v-if="item.error_info" class=" text-[#ff3f3f] font-bold">
                        {{ item.error_info }}
                      </div>
                    </div>
                    <div v-if="item.tableData && item.tableData.length > 0" class="max-h-[300px] overflow-auto">
                      <table class="replyTable">
                        <thead class="sticky top-0 z-10">
                          <tr>
                            <td v-for="(it, ind) in item.tableHeader" :key="ind" :class="ind === 0 ? 'first-column' : ''">
                              {{ it }}
                            </td>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(it, ind) in item.tableData[0].length" :key="ind">
                            <td v-for="(i, inx) in item.tableHeader.length" :key="i" :class="inx === 0 ? 'first-column' : ''">
                              {{ item.tableData[inx][ind] }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div v-if="item.chartJSON">
                      <div :id="`chart-box${index}`" class="w-full min-h-[400px] chart-box mt-4" />
                    </div>
                    <div v-if="!item.isMe && !chatLoading" class="flex justify-end items-center mt-2">
                      <span class="mr-2 text-sm leading-6 text-gray-400 feedback-desc">
                        内测阶段，为了让我更聪明，请留下您对<span class="font-bold">当前回答</span>的反馈🙇🏻🙇🏻🙇🏻➡
                      </span>
                      <a-tooltip title="是我想要的答案">
                        <img v-if="!like?.[index]" src="../images/good.png" width="20" height="20" class="mr-4 text-gray-400 cursor-pointer" @click="showComment('like', index, item)">
                        <img v-if="like?.[index]" src="../images/selectedGood.png" width="20" height="20" class="mr-4 text-gray-400 cursor-pointer">
                      </a-tooltip>
                      <a-tooltip title="不是我想要的答案">
                        <img v-if="!dislike?.[index]" src="../images/bad.png" width="20" height="20" class="cursor-pointer" @click="showComment('dislike', index, item)">
                        <img v-if="dislike?.[index]" src="../images/selectedBad.png" width="20" height="20" class="cursor-pointer">
                      </a-tooltip>
                      <div v-if="item.commentAreaVisible">
                        <a-textarea
                          v-model:value="item.comment"
                          class="my-2 rounded-md"
                          placeholder="请输入您的反馈意见"
                          :auto-size="{ minRows: 2 }"
                        />
                        <div class="flex  justify-between">
                          <a-button class="rounded-lg input-btn" type="primary" @click="submitComment(index, item)">
                            提交{{ state.curEvaluateType === 'like' ? '好评' : '差评' }}反馈
                          </a-button>
                          <a-button class="rounded-lg input-btn" type="primary" @click="() => item.commentAreaVisible = false">
                            取消
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <button class="scroll-to-bottom-btn" title="滚动到底部" :class="{ visible: showToBottom && chatList.length > 1 }" @click="scrollToBottom()">
          <DownOutlined class="arrow-down" />
        </button>
      </div>
      <div class="flex w-full flex-col">
        <div class="flex items-center flex-wrap gap-2 mt-2 justify-center">
          <div v-if="currentCategory?.length" class="font-bold">
            推荐问题：
          </div>
          <a-button
            v-for="(question, idx) in currentCategory"
            :key="idx"
            type="primary"
            ghost
            class="h-auto text-left truncate whitespace-normal rounded-md opacity-90 template-btn option-btn"
            @click="copyQuestionToInput(question)"
          >
            <a-tooltip :title="question" placement="top">
              <div class="ml-1.5 text-ellipsis">
                <TypeWriter :text="question" :ellipsis="true" />
              </div>
            </a-tooltip>
          </a-button>
        </div>

        <div class="flex w-full items-center justify-center py-2">
          <div
            :class="state.isInputFocused && 'focused'"
            class="flex items-center justify-center  gap-2 px-4 py-2 ml-2 rounded-md chat-input"
            style="width: calc(100% - 90px)"
          >
            <a-input-group compact>
              <a-textarea
                v-model:value="userInputMessage"
                :auto-size="{ minRows: 1, maxRows: 4 }"
                :placeholder="isMobileDevice ? '输入内容' : '输入内容开始聊天（Ctrl+Enter 换行）'"
                :size="isMobileDevice ? 'small' : 'large'"
                :bordered="false"
                :disabled="isAnswer"
                class="bg-transparent border-transparent search-input send-input"
                @keyup.enter="userHandleSend"
                @keydown.enter="preventEnter"
                @focus="state.isInputFocused = true"
                @blur="state.isInputFocused = false"
                @input="handleInputChange"
              />
              <div class="btns">
                <a-button
                  type="primary"
                  :size="isMobileDevice ? 'middle' : 'large'"
                  class="rounded-md clear-btn"
                  @click="handleClearChatInput"
                >
                  <img src="@/pages/party-building/images/clear.png" :width="isMobileDevice ? 20 : 30">{{ isMobileDevice ? '' : '清空' }}
                </a-button>
                <a-button
                  type="primary"
                  :size="isMobileDevice ? 'middle' : 'large'"
                  class="rounded-md clear-btn"
                  :class="{ recording: state.isRecording }"
                  @click="handleVoiceInput"
                >
                  <AudioOutlined :width="isMobileDevice ? 20 : 30" />{{ isMobileDevice ? '' : (state.isRecording ? '停止' : '语音') }}
                </a-button>
                <a-button
                  v-if="!isAnswer"
                  type="primary"
                  :size="isMobileDevice ? 'middle' : 'large'"
                  class="rounded-md input-btn"
                  @click="userHandleSend(userInputMessage, sessionId)"
                >
                  <send-outlined />{{ isMobileDevice ? '' : '发送' }}
                </a-button>
                <a-button
                  v-else
                  type="primary"
                  :size="isMobileDevice ? 'middle' : 'large'"
                  class="rounded-md input-btn"
                  @click="userHandlePause()"
                >
                  <pause-outlined />{{ isMobileDevice ? '' : '发送' }}
                </a-button>
              </div>
            </a-input-group>
            {{ similarQuestions }}
          </div>
        </div>
        <div class="text-[#a5a0a0] text-[12px] pr-2 bottom-tip text-center mb-3">
          <TypeWriter text="服务生成的所有内容均由人工智能模型辅助生成，其生成内容的准确性和完整性无法保证，不代表我们的态度或观点。" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.bk-wrapper {
  height:calc(100% - 60px) ;
}
.header-dot {
  margin-right: 8px;
  font-weight: bold;
}
.replyTable {
  font-size: 15px;
  width: 100%;
  border-collapse: collapse;
  border: 1px solid rgba(96, 109, 134, 0.25);
  border-radius: 10px 10px 0 0;
  overflow: hidden; /* 确保圆角效果 */
  background-color: white;
}

.replyTable thead {
  color: #606D86;
  background-color: #DDE9FF;
}

.replyTable thead tr:first-child td:first-child {
  border-top-left-radius: 10px;
}

.replyTable thead tr:first-child td:last-child {
  border-top-right-radius: 10px;
}

.replyTable td {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(96, 109, 134, 0.25);
  // border-right: 1px solid rgba(96, 109, 134, 0.25);
  text-align: center;
}

.replyTable td.first-column {
  text-align: left; /* 第一列靠左 */
  padding-left: 20px; /* 第一列左侧内边距稍大 */
  font-weight: 500;
}
.replyTable thead td.first-column {
 color:#000000
}
.replyTable tr:last-child td {
  border-bottom: none; /* 最后一行不加下边框 */
}
.replyTable td:last-child {
  border-right: none; /* 最后一列不加右边框 */
}

/* 可选：添加悬停效果 */
.replyTable tbody tr:hover {
  background-color: rgba(221, 233, 255, 0.3);
}
.chat-pop {
  line-height: 26px;
  letter-spacing: 0.05em;
  font-size: 15px;
  border-top-left-radius: 0;
  max-width: 100%;
  width: 100%;

}

.markdown-wrap-problem {
  font-weight: 600;
}
.chat-right {
  > div {
    flex-direction: row-reverse;
    .chat-pop {
      border-radius: 14px 24px 0px 14px;
      width: auto;
    }
    .chat-wrap {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      width: 100%;
    }
  }
}
.chat-wrap {
 width: 0px;
 flex: 1;
}

.category-item-btn {
  border:1px solid #4F52FF;
  color:#4F52FF;
  background-color:#fff !important;
}

.chatBoxWrap::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.chatBoxWrap::-webkit-scrollbar-thumb {
  /*滚动条里面深色条*/
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
}
.chatBoxWrap::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 10px;
  background: none;
}
.md-container{
  max-height:400px;
  overflow-y: auto;
}

.md-container::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.md-container::-webkit-scrollbar-thumb {
  /*滚动条里面深色条*/
  border-radius: 10px;
  background: #b8bcbf;
}
.md-container::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 10px;
  background: rgba(255, 255, 255, .3);
}

.gradient-text {
  font-size: 28px;
}
.gradient-wenan {
  background: linear-gradient(180deg, #00C3FF 0%, #5E64FF 34%, #6B48FF 49%, #0A4FFF 100%);
  -webkit-background-clip: text;
  color: transparent;
}

.option-btn{
  max-width: calc(50% - 60px);
  background: linear-gradient(to right, #FFFFFF 0%, #FAF7FF  100%);
  border: 1px solid rgba(96,109,134,0.2);
}
.btns{
  width: 100%;
  display: flex;
  justify-content:flex-end;
  align-items: center;
}
.rotated {
  transform: rotate(180deg);
  transition: transform 0.3s;
}
.input-btn{
  width: 110px;
  border: none;
  color: #FFFFFF;
  background: #000000;
  border-radius: 23px !important;
}
.clear-btn{
  width: 110px;
  border: none;
  color: #4A4C4B;
  background: #FFFFFF;
  box-shadow: 0px 1px 4px 1px rgba(228,231,237,0.43);
  border-radius: 23px;
  border: 1px solid rgba(96,109,134,0.15);
  margin-right: 10px !important;
}

.ant-input:hover {
  border-color:#BD4FFF;
  border-right-width: 1px !important;
}

.focused {
  border: 2px solid rgba(79, 82, 255, 1);
  box-shadow: 0px 2px 4px 2px rgba(228,231,237,0.43);
}

::v-deep(.ant-collapse-header) {
  padding: 8px 16px !important;
}

::v-deep(blockquote) {
  // background-color: #c0afaf0d;
  border-left: 2px solid #C0CBD6;
  margin: 1em 0;
  padding: 0.5em 1em;
  color: #00000080;
  font-family: PingFangSC, PingFang SC;
  font-style:normal;

  // 字间距
}
/* 定义隐藏动画 */
@keyframes fadeOut {
  from {
      opacity: 1;
      max-height: 9000px;
      display: block;
      clip-path: inset(0px 0px 0px 0px); /* 上、右、下、左 */
    }
  to {
      opacity: 0;
      max-height: 0;
      display: none;
      clip-path: inset(0px 100% 100% 0px); /* 上、右、下、左 */
    }
}

/* 定义显示动画 */
@keyframes fadeIn {
  from {
      opacity: 0;
      max-height: 0;
      display: none;
       clip-path: inset(0px 100% 100% 0px); /* 上、右、下、左 */
    }
  to {
      opacity: 1;
      max-height: 9000px;
      display: block;
       clip-path: inset(0px 0px 0px 0px); /* 上、右、下、左 */
   }
}

::v-deep(.chatBoxWrapwrap) {
  p{
    margin: 0px;
  }
}
.robot-chat-box{
  ::v-deep(.markdown-wrap) {
    p{
      margin-top: 20px;
    }
  }
  ::v-deep(strong){
    font-size: 17px;
    color: #525cff;
  }
   ::v-deep(blockquote strong){
    font-size: 15px;
    color: #00000080;
  }
}
::v-deep(.my-answer-box){
  background: #FFF3E2;
  color: #262626;
  p{
    margin: 0px;
  }
}
::v-deep(.show-with-animation){
  animation: fadeIn 0.8s forwards;
}
::v-deep(.hide-with-animation) {
  /* 隐藏状态 */
  animation: fadeOut 0.8s forwards;
  overflow: hidden;
}
::v-deep(pre) {
  background-color: #f5f5f5;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

.category-select-wrapper {
  display: none;
}
.video-container{
  position: relative;
  width:200px;
  height:200px;
  margin: 0 auto;
  overflow: hidden;
  transition: all 0.5s ease-in-out;
}
.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  transition: all 0.5s ease-in-out;
}
.video-thumbnail{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.video-ended.video-container{
  width: 100px;
  height: 100px;
}
/* 视频结束后的样式 */
.video-ended .video-player {
  opacity: 0;
}

.video-ended .video-thumbnail {
  opacity: 1;
}

.circular-video-container {
  position: relative;
  width:100px;
  height:100px;
  margin: 0 auto;
  overflow: hidden;
  clip-path: circle(48% at center); /* 完美圆形裁剪 */
  transition: all 0.5s ease-in-out;
}

.circular-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: all 0.5s ease-in-out;
}
.circular-video-thumbnail{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.5s ease-in-out;
}
.circular-video-thumbnail-show{
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  opacity: 1;
  transition: all 0.5s ease-in-out;
}
/* 视频结束后的样式 */
.circular-video-ended .circular-video {
  opacity: 0;
}

.circular-video-ended .circular-video-thumbnail {
  opacity: 1;
}
.circular-video-ended.circular-video-container {
  width: 50px;
  height: 50px;
}

.chat-overflow{
  // height: 100px;
  // overflow-y:auto;
}
.robot-name {
  font-family: quote-cjk-patch,Inter,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Noto Sans,Ubuntu,Cantarell,Helvetica Neue,Oxygen,Open Sans,sans-serif;
  font-size: 28px;
  padding: 8px 0;
}
.xin-bao-description {
  font-family: quote-cjk-patch,Inter,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Noto Sans,Ubuntu,Cantarell,Helvetica Neue,Oxygen,Open Sans,sans-serif;
  font-size:24px;
  color: #606D86;
  line-height: 40px;
  text-align: left;
  // letter-spacing: 1px;
}
.chat-input {
  background: #FFFFFF;
  box-shadow: 0px 8px 14px 1px rgba(228,231,237,1);
  border-radius: 23px;
  border: 1px solid rgba(96,109,134,0.15);
}
 /* 悬浮按钮样式 */
.scroll-to-bottom-btn {
    position: fixed;
    left: 50%;
    bottom: 200px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transform: translateX(-50%);
    background-color: rgb(232,243,253);
    color: white;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    z-index: 9999999999;
}

.scroll-to-bottom-btn:hover {
    background-color: #3367d6;
    transform: scale(1.1);
    .arrow-down {
      color: #fdfdff;
    }
}

.scroll-to-bottom-btn.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 箭头图标 */
.arrow-down {
  color: #525CFF;
    // display: inline-block;
    // width: 0;
    // height: 0;
    // border-left: 10px solid transparent;
    // border-right: 10px solid transparent;
    // border-top: 15px solid rgb(20, 19, 19);
}
@media screen and (max-width: 868px) {
  .bottom-tip {
    display: none;
  }
  .robot-name {
    font-size: 18px; /* 20px */
    line-height: 1.75rem; /* 28px */
    padding: 8px 0;
    letter-spacing: 3px;
  }
  .xin-bao-description {
    font-size: 16px;
    color: #606D86;
    line-height: 40px;
    text-align: left;
  }
  .top-chat-wrap {
    display: none;
  }
  .avatar-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }
  .feedback-desc {
    font-size: 12px;
    line-height: 14px;
  }
  .chat-input {
    textarea.ant-input {
      min-height: 22px;
    }
  }
  .category-select-wrapper {
    display: inline;
    position: relative;
    width: 100vw;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .category-select-wrapper::-webkit-scrollbar {
    display: none;
  }
}
.recording {
  background: #ff4d4f !important;
  color: #fff !important;
  border-color: #ff4d4f !important;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}
</style>
