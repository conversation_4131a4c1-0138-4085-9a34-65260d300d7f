import { ref } from 'vue'
import { scrollChatBoxToBottom } from './index-src.js'
const WS_URL = 'ws://10.13.4.103:17860/ws'

export const sendWritableStream2WSMsg = writableStream =>
  new Promise((resolve, reject) => {
    const result = ref('')
    const ws = new WebSocket(WS_URL)
    // 将数据写入WebSocket
    writableStream.getWriter().write(ws)

    // 当WebSocket连接打开时，开始读取数据并发送
    ws.onopen = () => {
      const reader = writableStream.getReader()
      function readAndSend() {
        reader.read().then(({ done, value }) => {
          if (done) {
            return
          }
          ws.send(value)
          readAndSend()
        })
      }
      readAndSend()
    }
    ws.onmessage = (msg) => {
      console.log('接受到的消息：', msg)
      result.value = msg.data
      scrollChatBoxToBottom()
    }
  })

export const sendWSMsg = text =>
  new Promise((resolve, reject) => {
    const result = ref('')
    const ws = new WebSocket(WS_URL)

    ws.onerror = err => reject(err)
    ws.onopen = () => {
      // console.log('连接服务器端成功了...')
      ws.send(
        JSON.stringify({
          prompt: text,
          keyword: '',
          temperature: 0.1,
          top_p: 0.3,
          max_length: 2048,
          history: [],
        }),
      )
      resolve(result)
    }
    ws.onmessage = (msg) => {
      // console.log('接受到的消息：', msg)
      result.value = msg.data
      scrollChatBoxToBottom()
    }
    ws.onclose = () => {
      // console.log(`disconnected, %s result: %s${result.value}`)
      // resolve(result.toString())
    }
  })

const chat = async (text) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('POST', 'api/chat', true)
    xhr.withCredentials = true // 启用跨域请求和携带凭据
    xhr.setRequestHeader('Content-Type', 'application/json') // 设置请求头
    xhr.onload = () => {
      const res = xhr.response
      if (xhr.status === 200) {
        resolve(res)
      }
      else {
        reject(new Error('Network Error'))
      }
    }
    xhr.onerror = () => {
      reject(new Error('Network Error'))
    }
    xhr.send(JSON.stringify({
      prompt: text,
      temperature: 1.3,
      top_p: 0.5,
      max_length: 2048,
      history: [],
    }))
  })
}

export const requestGPT = async (text) => {
  // eslint-disable-next-line no-useless-catch
  try {
    const result = await chat(text)
    return result
  }
  catch (error) {
    throw error
  }
}
