<script setup>
import { computed, reactive, ref } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import uploadIcon from '../imgs/upload.png'
import modalBg from '../imgs/modalBg.png'
import { uploadFile } from '@/apis/manage/index'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['update:visible', 'upload'])
const formRef = ref()
const rules = {
  file_type: [{ required: true, message: '请选择文件类型' }],
  dateRange: [{ required: true, message: '请选择统计起止时间' }],
  excel_file: [{ required: true, message: '请选择文件' }],
}
const uploadForm = reactive({
  file_type: '',
  dateRange: [],
  excel_file: [],
})

const formData = ref(new FormData())
const modalVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
})

const modalStyle = {
  '--modal-bg': `url(${modalBg})`,
}

const handleFileUpload = async () => {
  await formRef.value.validate()
  uploadForm.excel_file.forEach((file) => {
    formData.value.append('excel_file', file.originFileObj)
  })
  let [start_time, end_time] = uploadForm.dateRange
  start_time = start_time && dayjs(start_time).format('YYYY-MM-DD')
  end_time = end_time && dayjs(end_time).endOf('day').format('YYYY-MM-DD')
  formData.value.append('file_type', uploadForm.file_type)
  formData.value.append('start_time', start_time)
  formData.value.append('end_time', end_time)
  if (!uploadForm.excel_file.length) {
    message.warning('请选择文件')
    return
  }
  await uploadFile(formData.value)
  message.success('文件上传成功')
  emit('upload')
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<template>
  <a-modal
    v-model:visible="modalVisible"
    title="数据上传"
    :mask-closable="false"
    class="modal-with-bg"
    :style="modalStyle"
    ok-text="保存"
    @ok="handleFileUpload"
    @cancel="handleCancel"
  >
    <a-form ref="formRef" :model="uploadForm" :rules="rules">
      <a-form-item label="文件类型" name="file_type" :label-col="{ span: 24 }">
        <a-select v-model:value="uploadForm.file_type" placeholder="请选择文件类型">
          <a-select-option value="1">
            CN2国际电力拥塞统计
          </a-select-option>
          <a-select-option value="2">
            国际网络能力情况
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="统计起止时间" name="dateRange" :label-col="{ span: 24 }">
        <a-range-picker
          v-model:value="uploadForm.dateRange"
          :placeholder="['开始日期', '结束日期']"
          style="width: 100%"
        />
      </a-form-item>
      <a-form-item label="文件" name="excel_file" :colon="false">
        <a-upload
          v-model:fileList="uploadForm.excel_file"
          :before-upload="() => false"
          :on-change="handleUploadChange"
          accept=".doc,.docx,.xls,.xlsx"
          :max-count="1"
        >
          <div class="upload-btn-box">
            <a-button class="upload-btn">
              <img :src="uploadIcon" class="upload-icon" alt="upload">
              点击上传
            </a-button>
            <span class="upload-hint">
              文件类型：doc、docx、xls、xlsx单个文件不能超过50M
            </span>
          </div>
        </a-upload>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style lang="less">
.modal-with-bg {
  .ant-modal-content {
    background: var(--modal-bg) center center / cover no-repeat !important;
  }

  .ant-modal-header {
    background: transparent !important;
    border-bottom: 1px solid rgba(232, 232, 232, 0.5);
    text-align: center;

    .ant-modal-title {
      color: #000000;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .ant-modal-body {
    background: transparent !important;
    padding: 24px;

    .ant-form-item-label {
      > label {
        font-size: 16px;
        color: #0D162A;
      }
    }

    .ant-select {
      .ant-select-arrow {
        color: #1677FF;
      }
    }

    .ant-picker {
      .ant-picker-suffix {
        color: #1677FF;
      }
    }

    .upload-btn-box {
      display: flex;
      align-items: center;

      .upload-btn {
        color: #4C52FF;
        border: 1px dashed #4C52FF;
        display: flex;
        align-items: center;

        .upload-icon {
          width: 16px;
          height: 16px;
          margin-right: 8px;
        }

        &:hover {
          opacity: 0.9;
        }
      }

      .upload-hint {
        margin-left: 12px;
        color: #999;
        font-size: 12px;
      }
    }
  }

  .ant-modal-footer {
    background: transparent !important;
    border-top: 1px solid rgba(232, 232, 232, 0.5);
    padding: 16px 24px;
    text-align: center;

    .ant-btn {
      &.ant-btn {
        width: 120px;
        border-radius: 6px;
        margin-right: 16px;
        border: 1px solid transparent;
        background:
          linear-gradient(white, white) padding-box,
          linear-gradient(222deg, rgba(189, 79, 255, 1), rgba(79, 82, 255, 1), rgba(51, 156, 255, 1)) border-box;

        &:hover {
          opacity: 0.9;
        }
      }

      &.ant-btn-primary {
        width: 200px;
        background: linear-gradient(304deg, #3D39FF 0%, #5E70FF 41%, #808EF6 100%);

        &:hover {
          opacity: 0.9;
        }
      }
    }
  }
}
</style>
