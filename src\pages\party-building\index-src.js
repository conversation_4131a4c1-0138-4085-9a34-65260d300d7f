import { reactive, ref } from 'vue'
// import axios from 'axios'
import { BkCategory, TbCategory } from './_config.js'
import dspIcon from '@/pages/party-building/images/deepseek.svg'
// import hxIcon from '@/pages/party-building/images/hxLogo.png'
// import yiweiIcon from '@/pages/party-building/images/yiwei.jpg'
// import zhipuIcon from '@/pages/party-building/images/zhipu.png'
// import kimiIcon from '@/pages/party-building/images/kimi.png'

// 1代表走大模型的路，2代表走传统搜索的路
export const hxAiLinks = [
  { value: '0001', label: '粮食百科AI' },
]

export const externalLinks = [
  { value: '0007', label: 'DeepSeek', url: 'https://chat.deepseek.com', icon: dspIcon },

]

export const currentCategory = ref()

export const SHOW_MODE_ENUM = {
  DRAWER: '1', // 右侧弹出
  PAGE: '2', // 页面展示
}

export const THEME_NAME_ENUM = {
  SGCB: 'SGCB', // 收购储备
  HZTJ: 'HZTJ', // 火灾统计
  HXKM: 'HXKM', // 华信知识库
  DEFAULT: 'default',
}

export const modelType = ref(hxAiLinks[0].value)

export const chatBoxWrap = ref(null)

// iframe src 参数
export const outerSrcParams = reactive({
  showMode: SHOW_MODE_ENUM.DRAWER,
  apiPrefix: THEME_NAME_ENUM.HXKM,
  isDrag: true,
  isSuggest: false,
  themeName: THEME_NAME_ENUM.HXKM,
  robotImageUrl: '/images/robot.png',
  userImageUrl: '/images/user-avatar.svg',
})

export const scrollChatBoxToBottom = () => {
  setTimeout(() => chatBoxWrap.value.scrollTop = chatBoxWrap.value.scrollHeight)
}

export const getOuterSrcParam = () => {
  const queryString = window.location.href?.split('?')?.[1]
  const paramsArr = queryString?.split('&')
  for (let i = 0; i < paramsArr?.length; i++) {
    const item = paramsArr[i]?.split('=')
    if (item && item.length === 2) {
      outerSrcParams[item[0]] = decodeURIComponent(item[1])
    }
  }
}

export const selectCategory = (template) => {
  currentCategory.value = template
}

// 获取一级类目（领域）
export const getVectorsByParent = (value) => {
  if (value === '0001') {
    return BkCategory
  }
  else {
    return TbCategory
  }
  // return axios.get(`${outerSrcParams?.apiPrefix || 'SGCB'}/api/hxkm/queryVectorsByParent`, {
  //   params: {
  //     category: value,
  //   },
  // })
}

