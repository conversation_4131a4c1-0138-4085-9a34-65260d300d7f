<script setup>
import { onMounted, ref, toRefs, watch } from 'vue'
import axios from 'axios'
import { Modal, message } from '@castle/ant-design-vue'
import debounce from 'lodash-es/debounce'
import {
  addDirectorEsAnswer,
  addQuestionExtend,
  deleteDirectorEsAnswer,
  deleteQuestionExtend,
  modifyDirectorEsAnswer,
  modifyQuestionExtend,
  searchStandardQuestion,
} from '@/apis/manage/index'
import IncidentTreeSelect from '@/components/IncidentTreeSelect.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  action: {
    type: String,
    default: '新增',
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  apiPrefix: {
    type: String,
    default: 'HZTJ',
  },
  type: {
    type: Number,
    default: 0, // 0 标准问 1 扩展问
  },
})

const emit = defineEmits(['close', 'refresh'])
const { visible, action, formData, type, apiPrefix } = toRefs(props)

const TYPE_ENUM = {
  STANDARD: 0, // 标准问
  EXTEND: 1, // 扩展问
}
const ACTION_TYPE_ENUM = {
  ADD: '新增',
  EDIT: '编辑',
  VIEW: '查看',
}

const rules = {
  bizType: [{ required: false, message: '请选择下钻类型', trigger: 'change' }],
  standardQuestion: [{ required: true, message: '请选择关联标准问', trigger: 'change' }],
  questionType: [{ required: true, message: '请选择问题类型', trigger: 'change' }],
  questionAbstract: [{ required: true, message: '请输入问题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入答案', trigger: 'blur' }],
}

// 问题类型
const typeOptions = [
  { label: '操作型', value: '操作型' },
  { label: '业务型', value: '业务型' },
]

const formRef = ref()
const incidentTreeSelectRef = ref(null)
const standardQuestionList = ref([])
const standardId = ref(props.formData.standardId)

// 关闭详情弹窗
const handleCancel = () => {
  emit('close')
}

// 查看、新增、修改
const handleOk = () => {
  if (action.value === ACTION_TYPE_ENUM.VIEW) {
    handleCancel()
  }
  else {
    formRef.value.validateFields().then(async (values) => {
      let params = {
        ...values,
        id: formData.value.id,
        standardId: props.type === TYPE_ENUM.EXTEND ? standardId.value : undefined,
        bizType:
          props.type === TYPE_ENUM.STANDARD && formData.value.bizType?.length > 0
            ? formData.value.bizType
            : undefined,
      }
      // 新增
      if (action.value === ACTION_TYPE_ENUM.ADD) {
        if (type.value === TYPE_ENUM.STANDARD) {
          await addDirectorEsAnswer(props.apiPrefix, params)
        }
        else {
          params = {
            standardId: formData.value.standardId,
            standardQuestion: formData.value.standardQuestion,
            questionAbstract: formData.value.questionAbstract,
          }
          await addQuestionExtend(props.apiPrefix, params)
        }
      }
      // 编辑
      else if (action.value === ACTION_TYPE_ENUM.EDIT) {
        if (type.value === TYPE_ENUM.STANDARD) {
          params.id = formData.value.standardId
          await modifyDirectorEsAnswer(props.apiPrefix, params)
        }
        else {
          await modifyQuestionExtend(props.apiPrefix, params)
        }
      }
      message.success('操作成功')
      handleCancel()
      emit('refresh')
    })
  }
}

const handleDelete = async () => {
  try {
    if (type.value === TYPE_ENUM.STANDARD) { // 标准问删除
      await deleteDirectorEsAnswer(props.apiPrefix, { idList: [formData.value.standardId] })
    }
    else { // 扩展问删除
      await deleteQuestionExtend(props.apiPrefix, { id: formData.value.id })
    }
    message.success('删除成功')
    handleCancel()
    emit('refresh')
  }
  catch (error) {
    message.error('删除失败')
  }
}

const handleSearch = debounce((value) => {
  formData.value.standardQuestion = value
  searchStandardQuestion(props.apiPrefix, { matchingWords: value || '' }).then((res) => {
    standardQuestionList.value = res?.map(item => ({
      ...item,
      label: item.questionAbstract,
      value: item.id,
    }))
  })
}, 300)

const handleStandardQuestionChange = (value) => {
  formData.value.standardQuestion = value
  standardId.value = standardQuestionList.value.find(item => item.questionAbstract === value)?.id
}

const handleIncidentTypeChange = (val) => {
  formData.value.bizType = val.value
}

// 问题类型发生变化，则重置下钻类型
const onQuestionTypeChange = () => {
  formData.value.bizType = undefined
}

watch(() => [props.type, props.formData.standardQuestion], (newVal) => {
  if (newVal[0] === TYPE_ENUM.EXTEND) {
    searchStandardQuestion(props.apiPrefix, { matchingWords: newVal[1] || '' }).then((res) => {
      standardQuestionList.value = res?.map(item => ({
        ...item,
        label: item.questionAbstract,
        value: item.id,
      }))
    })
  }
}, { immediate: true })
</script>

<script>
export default {
  name: 'AddQuestionModal',
}
</script>

<template>
  <a-modal
    v-model:visible="visible"
    :title="`${action}${type === TYPE_ENUM.STANDARD ? '标准问' : '扩展问'}`"
    :mask-closable="false"
    :destroy-on-close="true"
    width="50vw"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div v-if="action !== ACTION_TYPE_ENUM.ADD" class="del-button">
      <a-popconfirm
        title="确定删除当前问题吗?"
        ok-text="确认"
        cancel-text="取消"
        @confirm="handleDelete"
      >
        <a href="#">删除</a>
      </a-popconfirm>
    </div>

    <a-form ref="formRef" :model="formData" :rules="action !== ACTION_TYPE_ENUM.VIEW ? rules : []" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
      <a-form-item v-if="type === 0" label="问题类型" name="questionType">
        <a-select
          v-model:value="formData.questionType"
          :options="typeOptions"
          :disabled="action === ACTION_TYPE_ENUM.VIEW"
          allow-clear
          placeholder="请选择问题类型"
          @change="onQuestionTypeChange"
        />
      </a-form-item>
      <a-form-item v-if="type === TYPE_ENUM.STANDARD" label="下钻类型" name="bizType">
        <IncidentTreeSelect
          ref="incidentTreeSelectRef"
          v-model:value="formData.bizType"
          :form-type="true"
          :biz-type-ids="formData.bizType"
          :api-prefix="apiPrefix || 'HZTJ'"
          :question-type="formData.questionType"
          @update-value="(val) => handleIncidentTypeChange(val)"
        />
      </a-form-item>
      <a-form-item v-else-if="type === TYPE_ENUM.EXTEND" label="关联标准问" name="standardQuestion">
        <a-select
          v-model:value="formData.standardQuestion"
          show-search
          allow-clear
          :default-active-first-option="false"
          :show-arrow="false"
          :filter-option="false"
          :not-found-content="null"
          :options="standardQuestionList"
          :disabled="action === ACTION_TYPE_ENUM.VIEW"
          placeholder="请输入标准问"
          @search="handleSearch"
          @change="handleStandardQuestionChange"
        />
      </a-form-item>
      <a-form-item label="问题" name="questionAbstract">
        <a-textarea
          v-model:value="formData.questionAbstract"
          :disabled="action === ACTION_TYPE_ENUM.VIEW"
          :auto-size="{ minRows: 5, maxRows: 10 }"
          placeholder="请输入问题"
          allow-clear
        />
      </a-form-item>
      <a-form-item v-if="type === TYPE_ENUM.STANDARD" label="答案" name="content">
        <a-textarea
          v-model:value="formData.content"
          :disabled="action === ACTION_TYPE_ENUM.VIEW"
          :auto-size="{ minRows: 5, maxRows: 10 }"
          placeholder="请输入答案"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style>
.del-button {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
  a {
    color: red;
  }
}
</style>
