import { nextTick, reactive, ref } from 'vue'
import axios from 'axios'
import dayjs from 'dayjs'
import { message } from '@castle/ant-design-vue'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import Plotly from 'plotly.js-dist'
import { getConversationDetail, getGenerateQues, getRelatedQuestions, submitCommit } from '@/apis/manage/index'
// 1代表走大模型的路，2代表走传统搜索的路
export const modelTypeOption = [
  { value: 1, label: '粮食百科AI' },
  { value: 2, label: '强条AI' },
]

export let like = reactive({})
export let dislike = reactive({})

export const similarQuestions = ref([])
export const session_id = ref()
export const creatLoading = ref(false)
export const currentCategory = ref()
export const handleScrolled = ref(false)
export const showToBottom = ref(false)

export const SHOW_MODE_ENUM = {
  DRAWER: '1', // 右侧弹出
  PAGE: '2', // 页面展示
}

export const THEME_NAME_ENUM = {
  SGCB: 'SGCB', // 收购储备
  HZTJ: 'HZTJ', // 火灾统计
  HXKM: 'HXKM', // 华信知识库
  DEFAULT: 'default',
}
const contentArr = ref([])
export const contentStr = ref('')

const typerIndex = ref(0)

const chartConfig = ref({
  displayModeBar: true, // 显示工具栏
  displaylogo: false, // 隐藏Plotly标志
  modeBarButtonsToRemove: [
    'lasso2d', 'select2d', 'zoom', 'autoScale2d',
    'resetScale2d', 'hoverClosestCartesian', 'hoverCompareCartesian',
    'sendDataToCloud', 'toggleSpikelines',
  ],
  toImageButtonOptions: { // 配置下载图片选项
    format: 'png', // 可选 'png', 'svg', 'jpeg', 'webp'
    filename: 'plot', // 下载文件名
    height: 600, // 图片高度
    width: 800, // 图片宽度
    scale: 1, // 缩放比例
  },
})
export const typerFinish = ref(true)
export const modelType = ref(modelTypeOption[0].value)

export const chatList = ref([])
export const userInputMessage = ref('')
export const chatBoxWrap = ref(null)
export const btnClickCount = ref(0) // 换一批按钮点击次数
export const replyLoading = ref(false)
export const chatLoading = ref(false)// 当前问题加载完成
export const categoryList = ref([])
export const isAnswer = ref(false)
export const isPause = ref(false)

export const robotImage = {
  png: '/images/logo.png',
  gif: '/images/logo.png',
}

// iframe src 参数
export const outerSrcParams = reactive({
  showMode: SHOW_MODE_ENUM.DRAWER,
  apiPrefix: THEME_NAME_ENUM.HXKM,
  isDrag: true,
  isSuggest: false,
  themeName: THEME_NAME_ENUM.HXKM,
  // robotImageUrl: robotImage.png,
  robotImageUrl: new URL('../images/robot.png', import.meta.url),
  // userImageUrl: '/images/user-icon.png',
  userImageUrl: new URL('../images/user-icon.png', import.meta.url),
})
export const setSessionId = (val) => {
  session_id.value = val
}
export const userHandlePause = () => {
  isPause.value = true
}

export const scrollChatBoxToBottom = (delay = 0) => {
  setTimeout(() => chatBoxWrap.value.scrollTop = chatBoxWrap.value.scrollHeight, delay)
}
export const scrollChatBoxToItem = (target, offset = 0) => {
  const element = document.getElementById(target)
  setTimeout(() => chatBoxWrap.value.scrollTop = chatBoxWrap.value.scrollHeight - element.scrollHeight - offset, 0)
}
export const getOuterSrcParam = () => {
  const queryString = window.location.href?.split('?')?.[1]
  const paramsArr = queryString?.split('&')
  for (let i = 0; i < paramsArr?.length; i++) {
    const item = paramsArr[i]?.split('=')
    if (item && item.length === 2) {
      outerSrcParams[item[0]] = decodeURIComponent(item[1])
    }
  }
}

export const TIPS_KEY_ENUM = {
  aiName: 'ROBOT_NAME',
  aiDescription: 'SAY_HI',
  guessText: 'GUESS',
  relatedText: 'RELATED',
}
export const gptTips = ref({
  aiName: '问答机器人',
  aiDescription: '作为您的智能小助手，我可以为您解答粮食行业规范、技术标准、科研成果等相关问题',
  aiSidebarDesc: 'AI小助手',
  guessText: '猜你想问的问题是：',
  relatedText: '或者你是想问：',
  changeCategoryTxt: '您已切换领域，请输入您想问的问题',
})
export const getGptTips = async () => {
  return await axios.get(`${outerSrcParams?.apiPrefix || 'SGCB'}/api/director/bureau/board/queryTips`)
}
export const handleChangeStatus = (item, index) => {
  if (contentStr.value && chatLoading.value) {
    return
  }
  item.isShow = !item.isShow
  const parent = document.getElementById(`markdown-wrap-${index}`)
  if (item.isShow) {
    for (let i = 0; i < 3; i++) {
      parent.children[i].classList.add('show-with-animation')
      parent.children[i].classList.remove('hide-with-animation')
    }
  }
  else {
    for (let i = 0; i < 3; i++) {
      parent.children[i].classList.remove('show-with-animation')
      parent.children[i].classList.add('hide-with-animation')
    }
  }
}
export const getHistoryChatListDetail = async () => {
  like = reactive({})
  dislike = reactive({})
  const res = await getConversationDetail({ session_id: session_id.value })
  chatList.value = res.map((item, index) => {
    const regeError = /<error_info>(.*)/
    const matchError = item.content.match(regeError)
    if (matchError && matchError[1]) {
      const error_info = matchError[1]
      item.error_info = error_info
      item.content = item.content?.replace(regeError, '')
    }
    const regelen = /<max_len>(.*)/
    const matchlen = item.content.match(regelen)
    if (matchlen && matchlen[1]) {
      try {
        const max_len = JSON.parse(matchlen[1])
        item.max_len = max_len
        item.content = item.content?.replace(regelen, '')
      }
      catch (e) {
        console.error('解析 JSON 失败:', e)
      }
    }
    const regex = /<plot>(.*)/
    const matchx = item.content.match(regex)
    if (matchx && matchx[1]) {
      try {
        // item.max_len = 10
        const chartJSON = JSON.parse(matchx[1])
        item.chartJSON = chartJSON
        if (item.chartJSON?.layout?.margin) {
          item.chartJSON.layout.margin.b = (10 * item.max_len + (item.max_len < 5 ? 10 : 0)) || 30
          item.chartJSON.layout.margin.r = item.max_len > 5 ? 14 + 7 * (item.max_len - 5) : 10
        }
        item.content = item.content?.replace(regex, '')
        setTimeout(() => {
          if (chartJSON) {
            Plotly.newPlot(`chart-box${index}`, item.chartJSON?.data, { ...item.chartJSON?.layout, height: 400 + 10 * (item.max_len || 0) }, chartConfig.value)
          }
        }, 300)
      }
      catch (e) {
        console.error('解析 JSON 失败:', e)
      }
    }
    const regey = /<df>(.*)/
    const matchy = item.content.match(regey)
    if (matchy && matchy[1]) {
      try {
        const jsonData = JSON.parse(matchy[1])
        item.tableHeader = Object.keys(jsonData)
        item.tableData = Object.keys(jsonData).map(key => Object.values(jsonData[key]))
        item.content = item.content?.replace(regey, '')
      }
      catch (e) {
        console.error('解析 JSON 失败:', e)
      }
    }

    item.content = item.content?.replace(/<\|im_end\|>/, '')?.replace(/<\|im_start\|>/, '')?.replace(/<think>/g, '<blockquote>')?.replace(/<\/think>/g, '</blockquote>\n\n')?.replace(/"}/, '')

    item.isShow = true
    like[index] = item.like
    dislike[index] = item.like !== null ? !item.like : null
    return item
  })
  nextTick(() => {
    const element = document.getElementById(`answer-anhor-${chatList.value.length - 2}`)
    scrollChatBoxToItem(`answer-anhor-${chatList.value.length - 1}`, element.scrollHeight)
    showToBottom.value = chatBoxWrap.value?.scrollTop + chatBoxWrap.value?.clientHeight + 10 < chatBoxWrap.value?.scrollHeight && chatBoxWrap.value?.scrollHeight >= chatBoxWrap.value?.clientHeight
  })
}

export const handleChat = async ({ question }) => {
  const payload = {
    query: question,
    session_id: session_id.value,
  }
  const controller = new AbortController()
  let totalAnswer = ''
  const tableObj = {}
  let chartJSON
  let docs = ''
  let trace_id
  let isThink = false
  let isDetail = false
  let isDdocs = false
  let max_len = ''
  let error_info = ''
  handleScrolled.value = false
  outerSrcParams.robotImageUrl = new URL('../images/robot.png', import.meta.url)
  outerSrcParams.robotVideoUrl = new URL('../images/robot-think.png', import.meta.url)
  fetchEventSource('/api/myvanna/create_ask', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      // 'Authorization': 'Bearer app-flilPBTyJnInRphi89dLn3Ig',
    },
    body: JSON.stringify(payload),
    signal: controller.signal,
    openWhenHidden: true,
    onmessage(msg) {
      if (msg.event === '' && msg.data) {
        const result = JSON.parse(msg.data)
        // 将<think>标签转换为Markdown的提示灰体字

        // console.log('--answer--', result.answer)

        if (result.answer && result.answer.includes('"result":"')) {
          // result.answer = result.answer.replace(/<think>/g, '<blockquote>').replace(/<\/think>/g, '</blockquote>')
          const content = result.answer?.split('"result":"')?.[1]
          totalAnswer = '<blockquote>'
          if (content) {
            totalAnswer += `${content}`?.replace(/"}/, '')
          }
          isThink = true
        }
        else if (result.answer && result.answer.includes('</details>')) {
          isThink = false
          isDetail = true
          totalAnswer += result.answer.replace(/<\/details>/g, '</blockquote>\n\n')
        }
        else if (result.answer && result.answer.includes('<think>')) {
          isThink = false
          isDetail = true
          totalAnswer += result.answer.replace(/<think>/g, '<blockquote class="chat-think">').replace(/<\/think>/g, '</blockquote>\n\n')
        }
        else if (result.answer && result.answer.includes('</think>')) {
          isThink = false
          isDetail = true
          totalAnswer += result.answer.replace(/<\/think>/g, '</blockquote>\n\n')
        }
        else if (result.answer && result.answer.includes('"docs":"')) {
          isDetail = false
          isDdocs = true
          const content = result.answer?.split('"docs":"')
          totalAnswer += (content?.[0] ? content[0]?.replace(/",\n/, '') : '')
          docs += (content?.[1] ? content[1] : '')
        }
        else if (result.answer && isDetail) {
          replyLoading.value = false
          totalAnswer += result.answer
        }
        else if (result.answer && isThink) {
          replyLoading.value = false
          totalAnswer += result.answer?.replace('<summary> Thinking... </summary>', '<summary> 思考 </summary>')
        }
        else if (result.answer && isDdocs) {
          docs += result.answer
        }
        else if (result.answer) {
          totalAnswer += result.answer
        }
        contentStr.value = totalAnswer
        scrollChatBoxToBottom()
        showToBottom.value = chatBoxWrap.value?.scrollTop + chatBoxWrap.value?.clientHeight + 10 < chatBoxWrap.value?.scrollHeight
        if (result.plot) {
          // 表格
          const data = JSON.parse(result.plot)
          chartJSON = data
        }
        if (result.df) {
          // 图表
          const data = JSON.parse(result.df.replace('/\\/g', ''))
          Object.keys(data).forEach((key) => {
            tableObj[key] = Object.values(data[key])
          })
        }
        if (result.trace_id) {
          trace_id = result.trace_id
        }
        if (result.error_info) {
          error_info = result.error_info
        }
        if (result.max_len && chartJSON?.layout?.margin) {
          max_len = result.max_len
          chartJSON.layout.margin.b = (10 * max_len + (max_len < 5 ? 10 : 0)) || 30
          chartJSON.layout.margin.r = max_len > 5 ? 14 + 7 * (max_len - 5) : 10
        }
        if (isPause.value) {
          isAnswer.value = false
          const latest = chatList.value?.[chatList.value.length - 1]
          isAnswer.value = false
          latest.content = totalAnswer?.replace(/<\|im_end\|>/, '')?.replace(/<\|im_start\|>/, '')?.replace(/<think>/g, '<blockquote>')?.replace(/<\/think>/g, '</blockquote>\n\n')?.replace(/"}/, '')
          latest.isShow = true
          if (docs && typeof docs === 'string') {
            docs = docs?.replace(/"}/, '')?.replace(/"\n}/, '')
            latest.docs = docs.split(/(?=【)/g)?.filter(item => item.trim() !== '')
            if (latest.docs?.length > 0) {
              latest.docs = latest.docs.map((item) => {
                const arr = item?.split('】')
                return {
                  title: arr?.[0]?.replace(/【/, ''),
                  content: arr?.[1],
                }
              })?.filter(temp => temp.title && temp.content?.length > 50)
            }
          }
          controller.abort()
        }
      }
    },
    onclose() {
      // eslint-disable-next-line no-console
      console.info('------fetch event source closed-----')
      const latest = chatList.value?.[chatList.value.length - 1]
      isAnswer.value = false
      replyLoading.value = false
      chatLoading.value = false
      latest.content = totalAnswer?.replace(/<\|im_end\|>/, '')?.replace(/<\|im_start\|>/, '')?.replace(/<think>/g, '<blockquote class="chat-think">')?.replace(/<\/think>/g, '</blockquote>\n\n')?.replace(/"}/, '')
      latest.tableHeader = Object.keys(tableObj)
      latest.tableData = Object.keys(tableObj).map(key => Object.values(tableObj[key]))
      latest.chartJSON = chartJSON
      latest.isShow = true
      if (docs && typeof docs === 'string') {
        docs = docs?.replace(/"}/, '')?.replace(/"\n}/, '')
        latest.docs = docs.split(/(?=【)/g)?.filter(item => item.trim() !== '')
        if (latest.docs?.length > 0) {
          latest.docs = latest.docs.map((item) => {
            const arr = item?.split('】')
            return {
              title: arr?.[0]?.replace(/【/, ''),
              content: arr?.[1],
            }
          })?.filter(temp => temp.title && temp.content?.length > 50)
        }
      }
      latest.trace_id = trace_id
      latest.error_info = error_info
      latest.max_len = max_len
      outerSrcParams.robotImageUrl = new URL('../images/logo.png', import.meta.url)
      controller.abort()
      creatLoading.value = false
      nextTick(() => {
        if (chartJSON) {
          Plotly.newPlot(`chart-box${chatList.value.length - 1}`, chartJSON?.data, { ...chartJSON?.layout, height: 400 + 10 * (max_len || 0) }, chartConfig.value)
        }

        scrollChatBoxToItem(`answer-anhor-${chatList.value.length - 1}`)
        handleChangeStatus(latest, chatList.value.length - 1)
      })
    },
    onerror(err) {
      console.error('Error occurred:', err)
      isAnswer.value = false
      replyLoading.value = false
      const latest = chatList.value?.[chatList.value.length - 1]
      isAnswer.value = false
      latest.content = totalAnswer
      latest.docs = docs
      controller.abort()
      message.warning('前方拥挤，请您稍后再提问~ ')
      throw err // 必须throw才能停止
    },
  })

  return {}
}

export const handleGPTChat = async ({ question }) => {
  const { needRedirectGpt, answer, docs, ...rest } = await handleChat({ question })
  const gptContent = answer || ''
  return {
    content: gptContent,
    needRedirectGpt,
    docs,
    ...rest,
  }
}

export const queryNextTemplate = async ({ question, page, pageSize }) => {
  return axios.get(
    `${outerSrcParams?.apiPrefix || 'SGCB'}/api/director/bureau/board/query/next?question=${question}&page=${page}&pageSize=${pageSize}`,
  )
}

export const handleInputChange = async () => {
  console.log(userInputMessage.value, 'value')

  const { kudian_list } = await getRelatedQuestions(userInputMessage.value)
  console.log(kudian_list)

  similarQuestions.value = kudian_list || []
}
export const updateVisitCount = () => {
  const formData = new FormData()
  formData.append('content_vectordb', currentCategory.value.content_vectordb)
  return axios.post(`${outerSrcParams?.apiPrefix || 'SGCB'}/api/addVisitCount`, formData)
}

// 构造对话对象工厂函数
export const createChat = ({
  content, isMe, templateList, time, templateParams, templateInfo, isNeedRedirectGpt, questionAbstract, responseId, docs, isStatement = false,
}) => {
  const isNeedTemplateParams = Boolean(templateParams)
  const isNeedShowTemplateList = templateList && templateList.length
  return {
    content,
    isMe,
    templateList,
    isNeedShowTemplateList,
    time: dayjs(time || new Date()).format('YYYY-MM-DD HH:mm:ss'),
    templateParams,
    templateInfo,
    isNeedTemplateParams,
    isNeedRedirectGpt,
    questionAbstract,
    responseId,
    docs,
    isStatement,
  }
}

export const formatDocsItem = (value, type) => {
  const temp = value.replace(/<td/gi, '<td style="border:1px solid #333"')
    .replace(/<\/td>/gi, '</td>').replace(/<tr/gi, '<tr style="border:1px solid #333"')
    .replace(/<\/tr>/gi, '</tr>').replace(/<table/gi, '<table style="max-width:90%;"')
    .replace(/<\/table>/gi, '</table>')
  return `${temp.split('@@@')[type]}`
}

const typerChat = (content) => {
  if (content) {
    contentStr.value = ''
    typerIndex.value = 0
    contentArr.value = content.split('')
    typerFinish.value = false
  }
  const contentArrLength = contentArr.value.length
  if (contentArrLength > 0 && typerIndex.value < contentArrLength) {
    contentStr.value += contentArr.value[typerIndex.value]
    typerIndex.value++
    setTimeout(() => typerChat(), 50)
  }
  else {
    setTimeout(() => {
      typerFinish.value = true
      scrollChatBoxToBottom()
    }, 500)
  }
  scrollChatBoxToBottom()
}

const replyChatMessage = async ({ content, time, templateList, templateParams, templateInfo, needRedirectGpt, questionAbstract, responseId, docs }) => {
  chatList.value.push(createChat({
    content,
    isMe: false,
    time,
    templateList,
    templateParams,
    templateInfo,
    isNeedRedirectGpt: needRedirectGpt,
    questionAbstract,
    responseId,
    docs,
  }))
  const curListLen = chatList.value.length
  if (curListLen > 3 && !chatList.value[curListLen - 3].isMe) {
    chatList.value[curListLen - 3].isNeedShowTemplateList = 0
  }
  if (!needRedirectGpt) {
    typerChat(content)
  }

  scrollChatBoxToBottom()
}

const sendChatMessage = (content) => {
  chatList.value.push(createChat({
    content,
    isMe: true,
  }))
  scrollChatBoxToBottom()
}
export const sendCategoryChangeMsg = () => {
  // chatList.value.push(createChat({
  //   content: `您现在的提问域为：${currentCategory.value.category_name3}，该领域${currentCategory.value.vectordb_intro}请输入您想问的问题 ~`,
  //   isMe: false,
  //   isStatement: true,
  //   templateInfo: '您已切换领域，请输入您想问的问题~',
  // }))
  // scrollChatBoxToBottom()
}

export const selectCategory = (template) => {
  currentCategory.value = template
}

const clearChatMessage = () => {
  setTimeout(() => {
    userInputMessage.value = ''
  })
}

export const userHandleChat = async (e) => {
  if (e.ctrlKey) {
    userInputMessage.value += '\n'
    return
  }
  if (!(userInputMessage.value.trim()).length) {
    message.info('对话消息不能为空')
    clearChatMessage()
    return
  }
  replyLoading.value = true
  chatLoading.value = true
  isAnswer.value = true
  isPause.value = false
  sendChatMessage(userInputMessage.value)
  clearChatMessage()
  const { content, time, templateList, templateParams, templateInfo, needRedirectGpt, questionAbstract, responseId, docs } = await handleGPTChat({ type: 'question', question: userInputMessage.value })
  replyChatMessage({ content, time, templateList, templateParams, templateInfo, needRedirectGpt, questionAbstract, responseId, docs })
  // updateVisitCount()
}
export const userHandleFirstChat = async (e) => {
  creatLoading.value = true
  userHandleChat(e)
}
export const preventEnter = e => e.preventDefault()

// 获取一级类目（领域）
export const getVectorsByParent = async () => {
  const res = await getGenerateQues({ search_nums: 2 })
  return res

  // return ['IP网络在欧洲境内带宽是多少？', 'IP网络从欧洲至北美方向带宽是多少，至中东非方向多少？']
  // return BkCategory
  // return axios.get(`${outerSrcParams?.apiPrefix || 'SGCB'}/api/hxkm/queryVectorsByParent`, {
  //   params: {
  //     category: '0001',
  //   },
  // })
}

export const getCategoryList = async () => {
  categoryList.value = await getVectorsByParent()
  currentCategory.value = categoryList.value
}

/** ****************** 评价 start ********************* */

export const submitEvaluation = async (msg_id, qa_value, comment) => {
  return await submitCommit({
    msg_id: String(msg_id),
    like: qa_value,
    comment,
  })
}
export const handleEvaluate = async (type, index, item) => {
  like[index] = type === 'like' ? !like[index] : false
  dislike[index] = type === 'dislike' ? !dislike[index] : false
  let qa_value = null
  if (like[index]) {
    qa_value = true
    item.commentAreaVisible = false
  }
  else if (dislike[index]) {
    qa_value = false
  }
  await submitEvaluation(item.trace_id, qa_value, item.comment)
  message.success('该回答评价反馈成功')
}

// 清屏
export const handleClearChatList = () => {
  chatList.value = []
  like = reactive({})
  dislike = reactive({})
}

