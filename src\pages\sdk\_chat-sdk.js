const sdk = class ChatSDK {
  constructor({
    rootDom = document.body,
    url = '',
    showMode = '1',
    apiPrefix = '',
    isDrag = true,
    themeName = 'SGCB',
    robotImageUrl = '',
    userImageUrl = '',
    customParams = {},
  }) {
    this.rootDom = rootDom
    this.url = url
    this.showMode = showMode
    this.apiPrefix = apiPrefix
    this.isDrag = isDrag
    this.themeName = themeName
    this.robotImageUrl = robotImageUrl
    this.userImageUrl = userImageUrl
    this.customParams = customParams
  }

  isShow = false
  toggleShowDom(status) {
    this.isShow = !this.isShow
    this.iframeDom.classList[status ? 'add' : 'remove']('__GPT__show')
    this.bubbleDom.classList[status ? 'add' : 'remove']('__GPT__hide')
    this.maskDom.classList[status ? 'add' : 'remove']('__GPT__show')
  }

  offset = [0, 0]
  isDown = false
  createBubbleDom() {
    let lastOffset = null
    this.bubbleDom = document.createElement('div')
    this.bubbleDom.style.backgroundImage = `url(${this.robotImageUrl})`
    this.bubbleDom.classList.add('__GPT__bubble-wrap')

    // 添加左上角提示语
    this.tooltipDom = document.createElement('div')
    this.tooltipDom.classList.add('__GPT__tooltip')
    this.appendDomToBody(this.tooltipDom, this.bubbleDom)

    this.bubbleDom.addEventListener('click', (event) => {
      event.stopPropagation()
    }, true)

    this.bubbleDom.addEventListener('mousedown', (e) => {
      e.stopPropagation()
      this.isDown = true
      this.offset = [
        this.bubbleDom.offsetLeft - e.clientX,
        this.bubbleDom.offsetTop - e.clientY,
      ]
      lastOffset = [this.bubbleDom.style.left, this.bubbleDom.style.top]
    }, true)

    this.bubbleDom.addEventListener('mouseup', (e) => {
      this.isDown = false
      // 如果没有对气泡进行拖拽，则显示气泡
      if (this.bubbleDom.style.left === lastOffset?.[0] && this.bubbleDom.style.top === lastOffset?.[1]) {
        e.stopPropagation()
        this.toggleShowDom(true)
      }
      else {
        lastOffset = this.offset
      }
    }, true)

    document.addEventListener('mousemove', (e) => {
      if (this.isDown && this.isDrag) {
        this.bubbleDom.style.left = `${e.clientX + this.offset[0]}px`
        this.bubbleDom.style.top = `${e.clientY + this.offset[1]}px`
      }
    }, true)
  }

  createIframeDom() {
    this.maskDom = document.createElement('div')
    this.maskDom.classList.add('__GPT__chat-mask')
    this.iframeDom = document.createElement('iframe')
    this.maskDom.appendChild(this.iframeDom)
    const customParamsPath = Object.keys(this.customParams).length > 0 ? `&${Object.keys(this.customParams).map(key => `${key}=${this.customParams[key]}`).join('&')}` : ''
    this.iframeDom.src = `${this.url}?themeName=${this.themeName}&apiPrefix=${this.apiPrefix}&showMode=${this.showMode}&robotImageUrl=${encodeURIComponent(this.robotImageUrl)}&userImageUrl=${encodeURIComponent(this.userImageUrl)}&isIframe=true${customParamsPath}`
    this.iframeDom.classList.add('__GPT__chat-wrap')
  }

  appendDomToBody(dom, parentDom) {
    parentDom.appendChild(dom)
  }

  removeDomToBody(dom, parentDom) {
    parentDom.removeChild(dom)
  }

  onClickOutside(ele, cb) {
    document.addEventListener('click', (event) => {
      if (!ele.contains(event.target) && this.isShow) {
        cb()
      }
    })
  }

  init() {
    this.createBubbleDom()
    this.createIframeDom()
    this.appendDomToBody(this.bubbleDom, this.rootDom)
    this.appendDomToBody(this.maskDom, this.rootDom)
    this.onClickOutside(this.iframeDom, () => {
      this.toggleShowDom(false)
    })
  }

  remove() {
    this.removeDomToBody(this.bubbleDom, this.rootDom)
    this.removeDomToBody(this.maskDom, this.rootDom)
  }
}

export default sdk
