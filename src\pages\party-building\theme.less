.SGCB-layout {
  --sgcb-background-color: rgb(15, 35, 80);
  --my-chat-box-br-color: #21cdcf;
  --my-chat-text-color: #042e5b;
  --robot-chat-box-color: #0d4c84;
  --text-color: #fff;
}

.HZTJ-layout {
  --sgcb-background-color: rgba(237, 245, 255, 0.9);
  --my-chat-box-br-color: rgb(13, 66, 141);
  --my-chat-text-color: #fff;
  --robot-chat-box-color: transparent;
  --text-color: #000;
}

.HXKM-layout {
  --sgcb-background-color: rgba(237, 245, 255, 0.9);
  --my-chat-box-br-color: rgb(13, 66, 141);
  --my-chat-text-color: #fff;
  --robot-chat-box-color: transparent;
  --text-color: #000;
}

.layout {
  background-color: var(--sgcb-background-color);
  color: var(--text-color);
  h1 {
    color: var(--text-color);
  }
  .side-bar {
    background-color: var(--robot-chat-box-color);
    // filter: brightness(160%);
    .history-card {
      border-width: 1px;
      background-color: var(--sgcb-background-color);
      // filter: brightness(160%);
      border-color: var(--text-color);
      svg {
        fill: var(--text-color);
        opacity: 0.8;
      }
    }
    .search-input {
      border-radius: 6px;
      border: solid 1px var(--text-color);
      background-color: var(--robot-chat-box-color);
      // filter: brightness(160%);
      svg {
        fill: var(--text-color);
        opacity: 0.8;
      }

      input, button, .ant-input-group-addon {
        border-width: 1px;
        background: none;
        border: none;        
        &:focus {
          box-shadow: none;
        }
      }
    }
  }
  .top-chat-wrap {
    background-color: var(--robot-chat-box-color);
  }
  .top-template-btn {
    color: var(--my-chat-box-br-color);
    border-color: var(--my-chat-box-br-color);
    text-shadow: none;
    background-color: var(--robot-chat-box-color);
    filter: brightness(90%);
    &:hover {
      color: var(--my-chat-box-br-color);
      border-color: var(--my-chat-box-br-color);
    }
  }

  .my-chat-box {
    background-color: var(--my-chat-box-br-color);
    color: var(--my-chat-text-color);
  }
  .robot-chat-box {
    background-color: var(--robot-chat-box-color);
    color: var(--text-color);
  }
  .template-btn {
    color: var(--text-color);
    border-color: var(--my-chat-box-br-color);
    text-shadow: none;
    background-color: var(--robot-chat-box-color);
  }
  .chat-input {
    background-color: var(--robot-chat-box-color);
    // filter: brightness(120%);
    border-color: var(--my-chat-box-br-color);
    color: var(--text-color);
    textarea.ant-input {
      color: var(--text-color);
    }
  }
  .chat-input-btn {
    background-color: var(--my-chat-box-br-color);
    color: var(--my-chat-text-color);
    border: none;
  }
}
