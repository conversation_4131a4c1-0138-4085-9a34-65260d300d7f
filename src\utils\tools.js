// 下载文件
import dayjs from 'dayjs'
import axios from 'axios'
export function downloadFile(obj, name, mimeType) {
  const link = document.createElement('a')
  link.style.display = 'none'
  link.setAttribute('download', `${name}`)
  // 字符内容转变成blob地址
  let url = null
  if (mimeType) {
    url = window.URL.createObjectURL(new Blob([obj], { type: mimeType }))
  }
  else {
    url = window.URL.createObjectURL(new Blob([obj]))
  }
  link.href = url
  // 自动触发点击
  document.body.appendChild(link)
  link.click()
  // 然后移除
  document.body.removeChild(link)
}

// 格式化时间：YYYY-MM-DD HH:mm
export const formatYmdHm = (date, type = 'YYYY-MM-DD HH:mm') => {
  if (!date) {
    return ''
  }
  return dayjs(date).format(type)
}

export const download = async (path, filename, config, method) => {
  method = method || 'post'
  config = config || {}
  const data = await axios({
    url: path,
    method,
    ...config,
    responseType: 'blob',
  })
  const a = document.createElement('a')
  a.href = URL.createObjectURL(data)
  a.download = filename
  a.style.display = 'none'
  document.body.appendChild(a)
  a.click()
  URL.revokeObjectURL(a.href)
  document.body.removeChild(a)
}
