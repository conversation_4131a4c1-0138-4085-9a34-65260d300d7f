import axios from 'axios'

// 导出
export function saveExportData(data) {
  const url = `${data.url}`
  return axios.post(url, data, {
    responseType: 'blob',
    noErrorHandler: true,
  })
}

// 获取数据库列表
export const getFileInfo = async (data) => {
  return axios.post(
    '/api/file/get_file_info', data,
  )
}
// 上传文件
export const uploadFile = async (data) => {
  return axios.post('/api/file/upload_file', data)
}
// 删除文件
export const deleteFile = async (data) => {
  return axios.post('/api/file/del_file', data)
}

// 获取推荐问题
export const getGenerateQues = async (params) => {
  return axios.post(
    '/api/myvanna/generate_ques',
    params,
  )
}

// 获取历史记录
export const getConversationHistory = async (data) => {
  return axios.post(
    '/api/myvanna/get_session_name',
    data,
  )
}

// 创建新对话
export const createNewConversation = async (data) => {
  return axios.get(
    '/api/myvanna/generate_session_id',
    data,
  )
}

// 获取对话详情
export const getConversationDetail = async (data) => {
  return axios.get(
    `/api/myvanna/get_history_session?session_id=${data.session_id}`,
  )
}

// 评价
export const submitCommit = async (data) => {
  return axios.post(
    '/api/myvanna/commit',
    data,
  )
}

// 获取相关问题
export const getRelatedQuestions = async (text) => {
  return axios.post(
    `/api/myvanna/text_similarity?text=${text}`,
  )
}
