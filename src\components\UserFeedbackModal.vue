<script setup>
import { onMounted, ref, toRefs, watch } from 'vue'
import axios from 'axios'
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { Modal, message } from '@castle/ant-design-vue'
import debounce from 'lodash-es/debounce'
import { datePickerProps } from '@castle/ant-design-vue/lib/date-picker/generatePicker/props'
import Viewer from 'viewerjs'
import IncidentTreeSelect from '@/components/IncidentTreeSelect.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  action: {
    type: String,
    default: '新增',
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  apiPrefix: {
    type: String,
    default: 'HZTJ',
  },
  userInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['close', 'refresh', 'submit'])
const { visible, action, formData, type, apiPrefix, userInfo } = toRefs(props)

const ACTION_TYPE_ENUM = {
  ADD: '新增',
  EDIT: '编辑',
  VIEW: '查看',
}

const rules = {
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
}

const formRef = ref()
const fileList = ref([])
const previewVisible = ref(false)
const previewImage = ref('')
const imgLoading = ref(false)
const viewer = ref()

const handlePreviewCancel = () => {
  previewVisible.value = false
  previewImage.value = null
}

const handlePreview = async (file) => {
  if (!viewer.value) {
    viewer.value = new Viewer(document.querySelector('.user-back-detail-image'), {
      zIndex: 99999,
    })
  }
  viewer.value.view(file?.index || 0)
}

const beforeUpload = (file) => {
  const types = ['png', 'jpg', 'jpeg']
  const fileType = file.name.split('.').slice(-1).join('')
  const isLt3M = file.size / 1024 / 1024 < 5

  if (!types.includes(fileType)) {
    message.warning('请检查文件格式，上传失败！')
    setTimeout(() => {
      fileList.value.pop()
    }, 50)
    return false
  }
  else if (fileList.value.length >= 3) {
    setTimeout(() => {
      fileList.value.pop()
    }, 50)
    message.warning('最多上传3个文件')
    return false
  }
  else if (!isLt3M) {
    message.warning('上传文件大小不能超过 5MB!')
    setTimeout(() => {
      fileList.value.pop()
    })
    return false
  }

  return true
}

const handleImgChange = ({ file }) => {
  if (file.status === 'uploading') {
    imgLoading.value = true
    return
  }
  if (file.status === 'done') {
    imgLoading.value = false
  }
  if (file.status === 'error') {
    imgLoading.value = false
    message.error('上传失败')
  }
}

// 关闭详情弹窗
const handleCancel = () => {
  formRef.value.resetFields()
  fileList.value = []
  emit('close')
}

// 查看、新增、修改
const handleOk = () => {
  if (action.value === ACTION_TYPE_ENUM.VIEW) {
    handleCancel()
  }
  else {
    formRef.value.validateFields().then(async (values) => {
      const params = {
        ...values,
        id: formData.value.id,
        // type: formData.value.type,
      }
      const pic = fileList.value?.map(item => item.response?.data?.filePath)?.join(',')
      const { userDept, userId, userName } = userInfo.value
      // await addFeedback({
      //   content: formData.value.content,
      //   pic,
      //   userDept,
      //   userId,
      //   userName,
      // })
      emit('submit', {
        content: formData.value.content,
        pic,
        userDept,
        userId,
        userName,
      })
      message.success('提交成功')
      handleCancel()
      emit('refresh')
    })
  }
}

watch(() => props.formData.id, (newValue) => {
  if (props.action === ACTION_TYPE_ENUM.VIEW && props.formData?.pic) {
    fileList.value = props.formData.pic?.map((item, index) => {
      return {
        uid: index,
        name: `反馈图片${index + 1}`,
        status: 'done',
        url: item,
        index,
      }
    })
  }
}, { immediate: true })
</script>

<script>
export default {
  name: 'UserFeedbackModal',
}
</script>

<template>
  <a-modal
    v-model:visible="visible"
    title="意见反馈"
    :mask-closable="false"
    width="44vw"
    :destroy-on-close="true"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-form ref="formRef" :model="formData" :rules="action !== ACTION_TYPE_ENUM.VIEW ? rules : []" :label-col="{ style: { width: '80px' } }" :wrapper-col="{ span: 24 }">
      <a-form-item label="反馈内容" name="content">
        <a-textarea v-model:value="formData.content" :disabled="action === ACTION_TYPE_ENUM.VIEW" :auto-size="{ minRows: 5, maxRows: 10 }" allow-clear />
      </a-form-item>
      <a-form-item label="上传图片">
        <a-upload
          v-model:file-list="fileList"
          list-type="picture-card"
          :disabled="action === ACTION_TYPE_ENUM.VIEW"
          :action="`${apiPrefix}/api/director/bureau/board/file/upload`"
          :before-upload="beforeUpload"
          :max-count="3"
          accept=".jpg,.png,.jpeg"
          class="user-back-detail-image"
          @preview="handlePreview"
          @change="handleImgChange"
        >
          <div v-if="fileList.length < 3">
            <LoadingOutlined v-if="imgLoading" />
            <PlusOutlined v-else />
            <div style="margin-top: 8px">
              上传图片
            </div>
          </div>
        </a-upload>
        <p v-if="action !== ACTION_TYPE_ENUM.VIEW">
          支持jpg、jpeg、png格式，最多上传3张，每张图片大小在5M内
        </p>
        <a-modal :visible="previewVisible" title="图片预览" :footer="null" @cancel="handlePreviewCancel">
          <img alt="example" style="width: 100%" :src="previewImage">
        </a-modal>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<style>
</style>
