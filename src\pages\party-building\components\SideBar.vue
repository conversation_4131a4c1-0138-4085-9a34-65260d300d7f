<script setup>
import { computed, defineProps, onMounted, ref } from 'vue'
import {
  ClockCircleOutlined,
  DownOutlined,
  LoadingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons-vue'
import RobotIcon from '../images/robot.png'
import {
  getConversationHistory,
} from '@/apis/manage'
const props = defineProps({
  handleCreatLoading: {
    type: Boolean,
    default: false,
  },
})

// 定义组件事件
const emit = defineEmits(['selectProject', 'newChat', 'collapseChange'])

// 状态管理
const historyExpanded = ref(true)
const searchKeyword = ref('')
const currentProjectIndex = ref()
const isCollapsed = ref(false)

// 模拟项目数据
const chatHistory = ref([])

// 根据搜索关键词过滤项目
const filteredChatHistory = computed(() => {
  if (!searchKeyword.value) {
    return chatHistory.value
  }
  return chatHistory.value.filter(project =>
    project.session_name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})

// 方法
const toggleHistory = () => {
  historyExpanded.value = !historyExpanded.value
}

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
  emit('collapseChange', isCollapsed.value)
}

const selectProject = (index) => {
  currentProjectIndex.value = index
  // 这里可以添加选择项目后的逻辑，例如发出事件通知父组件
  emit('selectProject', filteredChatHistory.value[index])
}

const handleNewChat = () => {
  currentProjectIndex.value = undefined
  // 新建对话的逻辑
  emit('newChat')
}
const setSelectIndex = (index) => {
  currentProjectIndex.value = index
}
const getHistoryList = async () => {
  const session_ids = JSON.parse(localStorage.getItem('session_ids')) || []
  const res = await getConversationHistory({ session_ids })

  // 根据 create_time 降序排序，最新的对话显示在前面
  chatHistory.value = res.sort((a, b) => {
    // 如果 create_time 是字符串格式的时间戳，先转换为数字
    const timeA = typeof a.create_time === 'string' ? new Date(a.create_time).getTime() : a.create_time
    const timeB = typeof b.create_time === 'string' ? new Date(b.create_time).getTime() : b.create_time
    // 降序排序
    return timeB - timeA
  })
}

onMounted(() => {
  getHistoryList()
})

defineExpose({
  getHistoryList,
  setSelectIndex,
})
</script>

<template>
  <div class="sidebar-wrapper" :class="{ collapsed: isCollapsed }">
    <!-- 收起/展开按钮 -->
    <div class="collapse-btn" @click="toggleCollapse">
      <MenuFoldOutlined v-if="!isCollapsed" />
      <MenuUnfoldOutlined v-else />
    </div>

    <!-- 顶部标题和图标 -->
    <div v-if="!isCollapsed" class="sidebar-header">
      <div class="logo-title">
        <div class="circular-image mr-2">
          <img class="logo-icon" :src="RobotIcon" alt="问数机器人">
        </div>
        <span class="title">问数机器人</span>
      </div>
      <!-- 新对话按钮 -->
      <div class="new-chat-wrapper">
        <div class="new-chat-btn" @click="handleNewChat">
          <PlusOutlined />
          <span class="ml-1 mr-1">新对话</span>
          <img src="../images/star-chat.png" alt="sparkle" class="sparkle">
        </div>
      </div>
    </div>

    <!-- 收起状态下只显示图标 -->
    <div v-if="isCollapsed" class="sidebar-header-collapsed">
      <div class="circular-image  mb-3">
        <img class="logo-icon" :src="RobotIcon" alt="问数机器人">
      </div>
      <div class="new-chat-btn-collapsed" @click="handleNewChat">
        <PlusOutlined />
      </div>
    </div>

    <!-- 历史记录部分 -->
    <div v-if="!isCollapsed" class="history-section">
      <div class="history-header" @click="toggleHistory">
        <span>
          <ClockCircleOutlined class="mr-1" />
          <span>历史记录</span>
        </span>
        <DownOutlined :class="{ rotated: historyExpanded }" style="color: #525CFF;" />
      </div>
    </div>

    <!-- 搜索框 -->
    <div v-if="!isCollapsed" class="search-wrapper">
      <div class="search-box">
        <input v-model="searchKeyword" type="text" placeholder="搜索">
        <SearchOutlined class="search-icon" />
      </div>
    </div>

    <!-- 项目列表 - 使用折叠面板效果 -->
    <div
      class="project-list"
      :class="{ 'collapsed-list': isCollapsed, 'expanded-list': historyExpanded && !isCollapsed, 'hidden-list': !historyExpanded && !isCollapsed }"
    >
      <transition-group name="list-transition">
        <!-- 创建新对话时的加载状态 -->
        <div v-if="handleCreatLoading" key="loading-item" class="project-item active loading-item">
          <div class="loading-spinner">
            <LoadingOutlined class="loading-icon" />
          </div>
          <span v-if="!isCollapsed" class="project-name">创建对话中...</span>
        </div>

        <div
          v-for="(item, index) in filteredChatHistory"
          :key="index"
          class="project-item"
          :class="{ 'active': currentProjectIndex === index, 'collapsed-item': isCollapsed }"
          @click="selectProject(index)"
        >
          <img src="../images/chat.png" alt="chat" class="mr-3">
          <span v-if="!isCollapsed" class="project-name">{{ item.session_name }}</span>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<style scoped>
.sidebar-wrapper {
  width: 240px;
  height: 100svh;
  border-right: 1px solid #e8eaed;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  background-image: url('../images/sidebarBg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-shadow: 0px 0px 10px 0px rgba(202,208,221,0.46);
  transition: width 0.3s ease;
}

.sidebar-wrapper.collapsed {
  width: 60px;
}

.collapse-btn {
  position: absolute;
  top: 10px;
  right: -12px;
  width: 24px;
  height: 24px;
  background-color: #fff;
  border: 1px solid #e8eaed;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sidebar-header {
  margin-bottom: 20px;
}

.logo-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  margin-top: 30px;
  padding: 0px 16px;
}
.circular-image{
  width: 34px;
  height: 34px;
  clip-path: circle(50% at 50% 50%);
  overflow: hidden;
}
.logo-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.sidebar-header-collapsed {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  margin-bottom: 20px;
}

.logo-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.new-chat-wrapper{
  padding: 10px 16px;
}

.new-chat-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #040927;
  color: white;
  padding: 10px;
  border-radius: 20px;
  cursor: pointer;
  position: relative;
  font-weight: 500;
}

.new-chat-btn-collapsed {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #040927;
  color: white;
  border-radius: 50%;
  cursor: pointer;
}

.history-section {
  margin-bottom: 16px;
  padding: 0px 16px;
}

.history-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 8px 0;
}

.history-header span {
  color: #393942;
}

.rotated {
  transform: rotate(180deg);
  transition: transform 0.3s;
}

.search-wrapper{
  padding: 0px 16px;
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #525CFF;
  border-radius: 7px;
  outline: none;
  padding-right: 30px;
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #525CFF;
}

.project-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  transition: all 0.3s ease;
  max-height: 100%;
}

.hidden-list {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  margin: 0;
  padding: 0;
}

.expanded-list {
  max-height: calc(100vh - 220px);
  opacity: 1;
}

.collapsed-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-height: calc(100vh - 120px);
}

/* 列表项过渡效果 */
.list-transition-enter-active,
.list-transition-leave-active {
  transition: all 0.3s;
}

.list-transition-enter-from,
.list-transition-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.list-transition-move {
  transition: transform 0.3s;
}

.project-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  border-radius: 6px;
  margin-bottom: 4px;
  color: #3F5C83;
  transition: all 0.2s ease;
  transform-origin: top;
}

.collapsed-item {
  padding: 10px 0;
  justify-content: center;
}

.project-item:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: translateX(2px);
}

.project-item.active {
  background-color: #fff;
  color: #000000;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
}

.project-icon {
  color: #4e89e8;
  margin-right: 8px;
}

.project-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sparkle {
  width: 16px;
  height: 16px;
}

/* 加载项样式 */
.loading-item {
  background-color: rgba(82, 92, 255, 0.05);
  border: 1px dashed #525CFF;
  position: relative;
  overflow: hidden;
}

.loading-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(82, 92, 255, 0.2),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  border-radius: 50%;
  background-color: rgba(82, 92, 255, 0.1);
}

.loading-icon {
  color: #525CFF;
  font-size: 16px;
  animation: spin 1.2s infinite linear;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
</style>

