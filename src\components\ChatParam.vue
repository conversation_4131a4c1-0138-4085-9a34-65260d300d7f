<script setup>
import { computed, ref, watch } from 'vue'
import dayjs from 'dayjs'
import areaCode from '@/assets/areaCode/sichuan.json'
const props = defineProps({
  templateParams: {
    type: Array,
    default: () => [],
  },
  templateInfo: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['fillAllParamValues'])

const isParam = (text) => {
  return /^\{.*\}$/.test(text)
}
const tipTextList = ref(props.templateInfo.tipText?.match(/{[^}]+}|[^{]+/g).map(i => ({ text: i, value: undefined, isParam: isParam(i) })))

const findAreaName = (arr, targetId) => {
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i]
    if (item.coder === targetId) {
      // 找到目标对象，返回同级的名称
      return item.name
    }
    else if (item.children && item.children.length > 0) {
      // 在子级中递归查找
      const siblingName = findAreaName(item.children, targetId)
      if (siblingName) {
        return siblingName
      }
    }
  }
}

const areaCodeOptions = ref(areaCode.filter(i => i.pcoder === '0').map(i => ({ ...i, isLeaf: false })))
const loadData = (selectedOptions) => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true
  const children = areaCode.filter(i => i.pcoder === targetOption.coder).map(i => ({ ...i, isLeaf: !areaCode.some(j => j.pcoder === i.coder) }))
  targetOption.loading = false
  targetOption.children = children
  areaCodeOptions.value = [...areaCodeOptions.value]
}

const handleFillParam = () => {
  const allParams = tipTextList.value.filter(i => i.isParam)
  const values = allParams.map(i => i.value)
  const allValuesIsNotEmpty = values.every(i => i)
  if (allValuesIsNotEmpty) {
    const paramValues = {}
    allParams.forEach((item, index) => {
      const isDate = dayjs(values[index]).isValid()
      // 处理时间
      if (isDate) {
        switch (item.text) {
          case '{date}':
            values[index] = dayjs(values[index]).format('YYYY-MM-DD')
            break
          case '{startTime}':
            values[index] = dayjs(values[index]).format('YYYY-MM-DD')
            break
          case '{endTime}':
            values[index] = dayjs(values[index]).format('YYYY-MM-DD')
            break
          case '{selectYear}':
            values[index] = dayjs(values[index]).format('YYYY')
            break
          case '{selectYearMonth}':
            values[index] = dayjs(values[index]).format('YYYY-MM')
            break
        }
      }
      // 处理AreaCode
      if (Array.isArray(values[index]) && values[index].length > 0) {
        values[index] = values[index][values[index].length - 1]
      }
      paramValues[item.text.replace(/\{|\}/g, '')] = values[index]
    })

    let paramIndex = 0
    const question = tipTextList.value.map((item, index) => {
      if (item.isParam) {
        // 处理AreaCode
        let currentParam = Object.values(paramValues)[paramIndex]
        if (item.text === '{areaCode}') {
          currentParam = findAreaName(areaCodeOptions.value, currentParam)
        }
        item.text = currentParam
        paramIndex++
      }
      return item.text
    }).join('')

    emit('fillAllParamValues', { paramValues, question, templateCode: props.templateInfo.templateCode })
  }
}

const disabledDate = (current) => {
  return current && current > dayjs().endOf('day')
}
const disabledStartDate = (current) => {
  let currentEndTime
  tipTextList.value.forEach((item) => {
    if (item.text === '{endTime}') {
      currentEndTime = item.value
    }
  })
  return (
    current
    && currentEndTime
    && current > dayjs(currentEndTime).endOf('day')
  )
}
const disabledEndDate = (current) => {
  let currentStartTime
  tipTextList.value.forEach((item) => {
    if (item.text === '{startTime}') {
      currentStartTime = item.value
    }
  })
  return (
    current
    && currentStartTime
    && current < dayjs(currentStartTime).endOf('day')
  )
}
</script>

<template>
  <div class="mt-2">
    <template v-for="item in tipTextList" :key="item">
      <span v-if="isParam(item.text)" class="px-2">
        <a-input v-if="item.text === '{text}'" v-model:value="item.value" placeholder="请输入" @change="handleFillParam" />
        <a-date-picker v-if="item.text === '{date}'" v-model:value="item.value" placeholder="请选择时间" :disabled-date="disabledDate" @change="handleFillParam" />
        <a-date-picker v-if="item.text === '{startTime}'" v-model:value="item.value" placeholder="请选择开始时间" picker="month" :disabled-date="disabledStartDate" @change="handleFillParam" />
        <a-date-picker v-if="item.text === '{endTime}'" v-model:value="item.value" placeholder="请选择结束时间" picker="month" :disabled-date="disabledEndDate" @change="handleFillParam" />
        <a-date-picker v-if="item.text === '{selectYear}'" v-model:value="item.value" placeholder="请选择年" picker="year" :disabled-date="disabledDate" @change="handleFillParam" />
        <a-date-picker v-if="item.text === '{selectYearMonth}'" v-model:value="item.value" placeholder="请选择年月" picker="month" :disabled-date="disabledDate" @change="handleFillParam" />
        <a-cascader
          v-if="item.text === '{areaCode}'"
          v-model:value="item.value"
          style="width: 140px"
          placeholder="请选择地区"
          :options="areaCodeOptions"
          :load-data="loadData"
          change-on-select
          :field-names="{ label: 'name', value: 'coder' }"
          expand-trigger="hover"
          @change="handleFillParam"
        />
        <a-select
          v-if="item.text === '{grainOilProName}'"
          ref="select"
          v-model:value="item.value"
          style="width: 140px"
          placeholder="请选择粮油性质"
          @change="handleFillParam"
        >
          <a-select-option value="省级储备">省级储备</a-select-option>
          <a-select-option value="市级储备">市级储备</a-select-option>
          <a-select-option value="县级储备">县级储备</a-select-option>
          <a-select-option value="其他储备">其他储备</a-select-option>
          <a-select-option value="商品粮">商品粮</a-select-option>
        </a-select>
      </span>
      <span v-else>{{ item.text }}</span>
    </template>
  </div>
</template>

<style lang="scss" scoped>

</style>
