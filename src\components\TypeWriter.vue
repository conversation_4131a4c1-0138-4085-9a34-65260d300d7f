<script setup>
import { defineProps, onMounted, ref } from 'vue'
const props = defineProps ({
  text: {
    type: String,
    default: '',
  },
  time: {
    type: Number,
    default: 0.5,
  },
  ellipsis: {
    type: Boolean,
    default: false,
  },
})
const show = ref(true)

const textChars = ref(props.text.split(''))
</script>

<template>
  <div class="typing-text" :class="{ 'text-ellipsis': ellipsis }">
    <span
      v-for="(char, index) in textChars"
      :key="index"
      :style="{ animationDelay: `${index * (time / textChars.length)}s` }"
    >{{ char }}</span>
  </div>
</template>

<style scoped>
.typing-text span {
  opacity: 0;
  display: inline-block;
  animation: charFadeIn 0.3s forwards;
}
.text-ellipsis{
  width: 100%;
  display: inline-block;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}
@keyframes charFadeIn {
  from {
    opacity: 0;
    /* transform: translateX(-10px); */
  }
  to {
    opacity: 1;
    /* transform: translateX(0); */
  }
}
</style>
