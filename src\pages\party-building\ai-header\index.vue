<route lang="json">
{
  "meta": {
    "title": "华信GPT",
    "icon": "DesktopOutlined",
    "requiresAuth": false,
    "sort": 1,
    "layout": "blank",
    "hideInMenu": true,
    "hideInBreadcrumb": true
  }
}
</route>

<script setup>
import { defineProps, ref } from 'vue'
import { useRouter } from 'vue-router'
import logoIcon from './imgs/logo.png'
// import databaseIcon from './imgs/database.png'
import userIcon from './imgs/user.png'
import talkIcon from './imgs/talk.png'
import ListIcon from './imgs/list.png'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  jumpPath: {
    type: String,
    default: '',
  },
  showLogo: {
    type: Boolean,
    default: false,
  },
})

const router = useRouter()

const modelType = ref(1)
const gotoPath = (path) => {
  router.push({
    name: path,
  })
}

const openUrl = (path, e) => {
  modelType.value = 1
  window.open(path)
  e.stopPropagation()
  e.preventDefault()
}
</script>

<template>
  <div class="header-wrapper">
    <div class="logo">
      <img v-if="showLogo" :src="logoIcon" alt="logo" class="icon-item">
    </div>
    <div v-if="title" class="title">
      {{ title }}
    </div>
    <div class="mb-5 page-content-header">
      <a-radio-group name="radioGroup" class="text-right model-radio-group" @change="handleGetCategoryList">
        <a-radio-button
          :value="1"
          style="border: none;background: none"
          :class="modelType === 1 ? 'active' : ''"
          class="text-[18px] font-bold text-[#707587] "
        >
          <div class="flex items-center">
            <img src="./imgs/logo.png" width="28" class="mr-1 mb-1">
            <span
              class="flex flex-col items-center leading-5"
            >
              问数机器人
            </span>
          </div>
        </a-radio-button>
        <a-radio-button
          :value="2"
          style="border: none;background: none"
          :class="modelType === 2 ? 'active' : ''"
          class="text-[18px] font-bold text-[#707587] "
          @click="(e) => openUrl('http://10.12.10.20:8080/hx-knowledge', e)"
        >
          <div class="flex items-center h-full">
            <span>
              华信百科AI
            </span>
          </div>
        </a-radio-button>
      </a-radio-group>
    </div>
    <div class="action-wrapper">
      <img v-if="props.jumpPath === 'party-building'" :src="talkIcon" alt="home" class="icon-item" @click="gotoPath(props.jumpPath)">
      <img v-if="props.jumpPath === 'database'" :src="ListIcon" alt="list" class="icon-item" @click="gotoPath(props.jumpPath)">
      <img :src="userIcon" alt="user" class="icon-item">
    </div>
  </div>
</template>

<style lang="less" scoped>
.header-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;

  .logo {
    width: 100px;
    .icon-item {
      width: 45px;
      height: 45px;
      object-fit: contain;
    }
  }
  .page-content-header {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .active{
      color: black !important;
      font-size: 20px;
    }
    .model-radio-group{
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }

  .action-wrapper {
    width: 100px;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .icon-item {
      width: 40px;
      height: 40px;
      object-fit: contain;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        opacity: 0.8;
      }
    }
  }
}
</style>
