<route lang="json">
{
  "name": "party-building",
  "meta": {
    "title": "问数机器人",
    "icon": "DesktopOutlined",
    "requiresAuth": false,
    "sort": 1,
    "layout": "blank",
    "hideInMenu": true,
    "hideInBreadcrumb": true
  }
}
</route>

<script setup>
import { computed, onMounted, ref } from 'vue'
import SideBar from './components/SideBar.vue'
import knBk from './kn-bk/index.vue'
import AiHeader from './ai-header/index.vue'
import './theme.less'
import {
  SHOW_MODE_ENUM,
  currentCategory,
  externalLinks,
  getOuterSrcParam,
  getVectorsByParent,
  hxAiLinks,
  modelType,
  outerSrcParams,
  selectCategory,
} from './index-src'
import { getGenerateQues } from '@/apis/manage/index'
// import backImg from '@/assets/images/BG-knowledge.png'

const categoryList = ref([])
const categoryOriginList = ref([])

const showDrawer = ref(false)
const keyword = ref()

const bkRef = ref(null)
const sidebarRef = ref(null)

const sessionId = ref('')
const creatLoading = ref(false)
const drawerBodyStyle = {
  backgroundImage: 'url(\'src/assets/images/bg.png\')',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat',
}

const handleSelectCategory = (item, modelType) => {
  if (modelType === '0001') {
    bkRef.value.handleSelectCategory(item)
  }
  else {
    qtRef.value.handleSelectCategory(item)
  }
  selectCategory(item)
}

const changeCategory = (val) => {
  currentCategory.value = val
}

const handleGetCategoryList = async () => {
  const res = await getGenerateQues({ search_nums: 2 })
  currentCategory.value = res

  // currentCategory.value = ['IP网络在欧洲境内带宽是多少？', 'IP网络从欧洲至北美方向带宽是多少，至中东非方向多少？']
  // const res = await getVectorsByParent(modelType.value)
  // categoryList.value = res.sort((a, b) => b.is_available - a.is_available)
  // categoryOriginList.value = JSON.parse(JSON.stringify(categoryList.value))

  // currentCategory.value = categoryList.value?.filter(i => i.is_available === 1)?.[0]
}

const handleFilterCategory = (val) => {
  if (val) {
    categoryList.value = categoryOriginList.value?.filter(i => (i.category_name3).includes(keyword.value))
  }
  else {
    categoryList.value = list
  }
}

const getAssetURL = (image) => {
  // 参数一: 相对路径   参数二: 当前路径的URL
  return new URL(`/src/pages/party-building/images/${image}.png`, import.meta.url).href
}

const handleOpenOutUrl = (value, url, e) => {
  if (['0003', '0004', '0005', '0006', '0007'].includes(value)) {
    url && window.open(url)
    e.stopPropagation()
    e.preventDefault()
  }
}

onMounted(async () => {
  handleGetCategoryList()
  // 获取url参数
  getOuterSrcParam()
  document.title = '问数机器人'
})
const currnetClassName = computed(() => {
  let className = 'SGCB-layout'
  if (outerSrcParams.themeName) {
    className = `${outerSrcParams.themeName}-layout`
  }
  return `layout ${className}`
})
const modelTypeOption = computed(() => {
  if (window.innerWidth <= 768) {
    return hxAiLinks
  }
  else {
    return hxAiLinks.concat(externalLinks)
  }
})

// 添加侧边栏收起状态
const sidebarCollapsed = ref(false)

// 处理侧边栏收起/展开
const handleSidebarCollapse = (collapsed) => {
  sidebarCollapsed.value = collapsed
}

const handleSelectProject = async (project) => {
  // 添加处理选择项目的逻辑
  bkRef.value.setSessionId(project?.session_id)
  await bkRef.value.getHistoryChatListDetail()
  bkRef.value.scrollChatBoxToBottom()
}

const handleNewChat = () => {
  // 处理新建对话的逻辑
  // 清空当前对话，开始新对话
  bkRef.value.handleClearChatList()
  bkRef.value.setSessionId(null)
}
const handleChatRefresh = async () => {
  await sidebarRef.value.getHistoryList()
  sidebarRef.value.setSelectIndex(0)
}
</script>

<template>
  <a-layout :class="`${currnetClassName} page-content`">
    <a-layout-sider
      :width="sidebarCollapsed ? 60 : 240"
      class="side-bar"
      :class="{ 'collapsed-sidebar': sidebarCollapsed }"
    >
      <!-- 添加侧边栏组件 -->
      <SideBar
        ref="sidebarRef"
        style="background-color: transparent;"
        :handle-creat-loading="creatLoading"
        @select-project="handleSelectProject"
        @new-chat="handleNewChat"
        @collapse-change="handleSidebarCollapse"
      />
    </a-layout-sider>
    <a-layout-content class="h-svh max-h-svh min-h-svh text-base diagonal-fade-in">
      <div class="flex flex-col items-center h-full pl-5 pr-5">
        <AiHeader class="fade-item" style="--delay: 0s" />
        <div class="mb-0 page-content-header fade-item" style="--delay: 0.2s" />
        <kn-bk v-show="modelType === '0001'" ref="bkRef" :category="currentCategory" class="fade-item" style="--delay: 0.4s" @change-category="changeCategory" @chat-refresh="handleChatRefresh" @creat-loading="(val) => creatLoading = val" />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<style lang="less" scoped>
/* 保留原有样式 */

/* 添加从左上至右下的淡入动画 */
.diagonal-fade-in {
  position: relative;
  overflow: hidden;
}

.fade-item {
  opacity: 0;
  animation: diagonalFadeIn 0.8s ease-out forwards;
  animation-delay: var(--delay, 0s);
}

@keyframes diagonalFadeIn {
  from {
    opacity: 0;
    transform: translate(-20px, -20px);
  }
  to {
    opacity: 1;
    transform: translate(0, 0);
  }
}

.category-item{
  border-radius:10px;
  background:#F5FAFF !important;
  box-shadow: 0px 0px 10px 0px rgba(167,176,192,0.2) !important;
  cursor: pointer;
}
.category-item:hover{
  border:1px solid #4F52FF;
  color:#4F52FF;
}
.category-item-light{
  border:1px solid #4F52FF;
  color:#4F52FF;
}
.category-item-grey{
  border:1px solid #999;
  color:#999;
  cursor: not-allowed;
}
.category-item-grey:hover{
  border:1px solid #999;
  color:#999;
  pointer-events: none;
}

.side-bar::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
}
.side-bar::-webkit-scrollbar-thumb {
  /*滚动条里面深色条*/
  border-radius: 10px;
  background: rgba(255, 255, 255, .3);
}
.side-bar::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 10px;
  background: none;
}

.collapsed-sidebar {
  width: 60px !important;
}
.staggered-grid {
  display: grid;
  // grid-template-columns: 1fr 1fr;
  grid-gap: 0.5rem;
}
.staggered-grid > div:nth-child(odd) {
  grid-row: span 1;
}

.page-content{
  background-image: url('@/assets/images/party-bg1.png');
  background-size: 100% 100%;
}
.active{
  color: black !important;
  font-size: 20px;
  top: 14px;
}
.menu-icon {
  display: none;
}
@media screen and (max-width: 868px) {
  .side-bar {
    display: none
  }
  .page-content-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .menu-icon {
    display: block;
    font-size: 16px;
    margin-bottom: 4px;
  }
  .menu-top-content {
    font-size: 14px;
  }
  .menu-top-content-list {
    list-style: none; /* 去掉默认的列表样式 */
    padding-left: 0; /* 去掉默认的内边距 */
    margin-top: 0; /* 去掉默认的外边距 */
    font-size: 16px;
    li {
      margin-bottom: 12px;
    }
    a {
      color: #000;
    }
  }
  .menu-bottom-content {
    color: #a5a0a0;
    font-size: 12px;
    margin-top: 20px;
    // 布局在底部
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 10px;
    // box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  }
}
.DeepSeek-label {
  position: relative;
  top: 6px;
}
</style>
