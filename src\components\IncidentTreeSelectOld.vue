<script setup>
import { defineExpose, onMounted, ref, toRefs, watch } from 'vue'
import { getIncidentType } from '@/apis/manage/index'

const props = defineProps({
  apiPrefix: {
    type: String,
    default: '',
  },
  // 数据回填用 bizTypeIds表示所有父级id
  bizTypeIds: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue', 'updateValue'])
const treeData = ref([])

const onLoadTreeData = async (node) => {
  if (node.isLeaf) {
    return
  }

  try {
    setTimeout(async () => {
      const res = await getIncidentType(props.apiPrefix, node.id)
      if (res && res.length) {
        const children = res?.map(item => ({
          ...item,
          isLeaf: false,
        }))

        // 递归找到节点并更新其子项
        const updateNode = (nodes, id) => {
          for (const n of nodes) {
            if (n.id === id) {
              n.children = [...children]
              return
            }
            if (n.children && n.children.length) {
              updateNode(n.children, id)
            }
          }
        }
        updateNode(treeData.value, node.id)
      }
      else {
      // 递归找到节点并更新其isLeaf
        const updateNode = (nodes, id) => {
          for (const n of nodes) {
            if (n.id === id) {
              n.isLeaf = true
              return
            }
            if (n.children && n.children.length) {
              updateNode(n.children, id)
            }
          }
        }
        updateNode(treeData.value, node.id)
      }
    }, 200)
  }
  catch (error) {
    // console.log('error', error)
  }
}

const getParentIds = (id) => {
  const result = []
  const dfs = (data, targetId, result) => {
    for (const i in data) {
      const item = data[i]
      if (item.id === targetId) {
        result.unshift(item.id)
        return true
      }
      if (item.children && item.children.length > 0) {
        const ok = dfs(item.children, targetId, result)
        if (ok) {
          result.unshift(item.id)
          return true
        }
      }
    }
    return false
  }
  dfs(treeData.value, id, result)
  return result
}

const handleChange = (value, selectedOptions) => {
  emit('updateValue', { value, selectedOptions })
  emit('update:modelValue', { value, selectedOptions })
}

const initTreeData = async () => {
  await getIncidentType(props.apiPrefix, 0).then((res) => {
    treeData.value = res?.map(item => ({
      ...item,
      isLeaf: false,
    }))
  })
}

onMounted(async () => {
  await initTreeData()
})

watch(() => props.bizTypeIds, async (newVal) => {
  // 数据回填时 逐层加载子节点
  if (newVal && newVal.length) {
    await initTreeData()
    newVal.forEach(async (id, index) => {
      await onLoadTreeData({ id })
    })
  }
}, { immediate: true })

defineExpose({ getParentIds })
</script>

<template>
  <a-tree-select
    tree-data-simple-mode
    style="width: 100%"
    allow-clear
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :tree-data="treeData"
    :field-names="{ children: 'children', label: 'value', key: 'id', value: 'id', pid: 'parentId' }"
    placeholder="请选择下钻类型"
    :load-data="onLoadTreeData"
    @change="handleChange"
  />
</template>
