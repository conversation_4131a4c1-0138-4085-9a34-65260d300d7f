// import { h } from 'vue'
// import Help from './components/Help.vue'
import BusinessTemplate from '@castle/business-components'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import 'viewerjs/dist/viewer.css'
import { notification } from '@castle/ant-design-vue'
import { getCodeMessages } from '@/utils/http-code-messages.js'

dayjs.locale('zh-cn')
export default {
  // APP 名称
  title: '问数机器人',

  // 登陆之后跳转的默认页面。注：用户默认跳转到此用户拥有权限的第一个页面
  homePath: '/party-building',

  // 水印文字
  watermarkContent: userInfo => userInfo?.name,

  // 多标签
  multiTab: true,

  // ConfigProvider 全局化配置 https://antdv.com/components/config-provider-cn
  themeConfigProvider: {},

  // 版权信息
  copyright: () => 'Copyright @2022 Huaxin. All Rights Reserved',

  // token 在 localStorage 存放的名称
  tokenKeyName: '',

  // http 通用拦截
  httpInterceptors: {
    request: (config) => {
      // config.headers.Authorization = 'Bearer xxx'
      return config
    },
    response: [(response) => {
      const { data, config, status } = response
      const { code } = data
      const { data: resData } = data

      if (config.url?.includes('text_similarity') && status === 200) {
        return data
      }
      if (config.noErrorHandler) {
        return response
      }
      else if (code !== 200 && code !== 302) {
        window.bus?.emit('CASTLE__globalLoading', false)
        if (code === 403) {
          notification.error({
            message: '无权限',
            description: data.message || data.msg,
          })
        }
        else if (code === 406) {
          notification.info({
            message: '非法参数',
            description: data.message || data.msg,
          })
        }
        else if (code === 500) {
          notification.error({
            description: data.message || data.msg || '系统内部错误',
          })
        }
        else if (code === 401) {
          localStorage.clear()
          // setTimeout(() => location.reload(), 500)
        }
        else {
          notification.error({
            message: data.message || data.msg || '未知错误',
          })
        }

        return Promise.reject(new Error(data.message || data.msg || 'Error'))
      }

      return resData
    },
    async (error) => {
      window.bus?.emit('CASTLE__globalLoading', false)
      if (error.response?.status === 401) {
        const text = error.response?.data?.msg || '用户信息输入有误，请重新输入！'
        notification.error({
          message: text,
        })
      }
      else if (error.response?.status === 302 && error.response?.data?.path === '/acws/login.jsp') {
        localStorage.clear()
        // setTimeout(() => location.reload(), 500)
      }
      else if (error.code === 'ERR_NETWORK' || error.code === 'ERR_BAD_REQUEST') {
        localStorage.clear()
        // setTimeout(() => location.reload(), 500)
      }
      else if (error.response?.status === 404) {
        notification.error({
          message: error.response?.data?.error || '非法请求，请重试！',
        })
      }
      else if (error.response) {
        const { status, statusText, data } = error.response
        const { message } = data
        // const token = getToken()

        if (status === 401 || status === 302) {
          notification.error({
            message: '操作未授权',
            description: message || '授权验证失败',
          })
          // if (token) {
          //   useUserStore()
          //     .logout()
          //     .then(() => {
          //       setTimeout(() => {
          //         window.location.reload()
          //       }, 1500)
          //     })
          // }
        }
        else {
          notification.error({
            message: `${status} ${statusText}` || '请求失败',
            description: message || getCodeMessages(status) || `未知错误 ${statusText}`,
          })
        }
      }

      return Promise.reject(error)
    }],
  },

  // 用户相关的处理实现
  userApiImplement: {
    // 密码RSA加密公钥，密钥对生成 http://web.chacuo.net/netrsakeypair
    rsaPublicKey: () => {
      return Promise.resolve(`MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtW7kaz2gLxctmW6zGSHu
      OQpMMzuYWNjX4FA0KZ7uOc7o/bs5tueRssYwA4IZCSmCbk/qH7BEEx5ppDlRIWHK
      mneYkcR0OHuvWODd0npL84ll8n5Ep8RwQWfuLz0xko7w+W2FgLdIefffHkRFIJir
      TBdRizrJDAZobyRUTTmeAt+NIg6YXNbRxupHpRgMrm5iZ+w38MiqEINzSJGqh5cU
      PjjCOiD6zbJQB1XC0tVY86desC/JYY3xAlucNQibYemEzWW9qrbX7bg3Dz6VxRVW
      oK21z+tVzW76F2jrd3w9AgR4vXpx3g439gET00mvjogYDZrDbM3ZRxCO89pxydqo
      9QIDAQAB`)
    },
    async getVerificationCode() {
      const { img } = await fetch('http://**********:8081/reserver/auth/code').then(r => r.json())
      return img
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    login(loginForm) {
      return Promise.resolve('xxxxx')
    },
    logout() {
      return Promise.resolve()
    },
    getUserInfo() {
      return Promise.resolve({
        name: '',
        role: 'client',
        roleName: '客户',
      })
    },
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    getPermissionData(userInfo) {
      return Promise.resolve({ permissionCodes: ['code1', 'code2'] })
    },
  },

  lifecycle: {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    beforeMount: (app) => {
      localStorage.setItem('token', 'xxxxx')
    },

    mounted: async (app) => {
      import('@/assets/styles/global-style.less')
      app.use(BusinessTemplate)
    },
  },

  // 顶部导航栏右侧用户菜单配置
  userNavigation: [{
    label: '登录',
    handleFn: router => router.push('/login'),
    displayFn: () => !localStorage.getItem('token'),
  }],

  // 顶部导航栏右侧自定义组件
  // userNavigationComponents: [h(Help)],

  // 暗黑模式配置
  // darkness: {
  //   showSwitch: true,
  //   defaultMode: 'light',
  // },
}
