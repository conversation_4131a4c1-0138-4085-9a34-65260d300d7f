<script setup>
import { defineExpose, onMounted, ref, toRefs, watch } from 'vue'
import { message } from '@castle/ant-design-vue'
import { getIncidentType } from '@/apis/manage/index'

const props = defineProps({
  formType: {
    type: Boolean,
    default: false,
  },
  apiPrefix: {
    type: String,
    default: '',
  },
  // 数据回填用 bizTypeIds 表示所有父级id
  bizTypeIds: {
    type: Array,
    default: () => [],
  },
  questionType: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'updateValue'])
const treeData = ref([])

const onLoadTreeData = async (selectedOptions) => {
  const node = selectedOptions[selectedOptions.length - 1]
  if (!node || node?.isLeaf) {
    return
  }

  try {
    const res = await getIncidentType(props.apiPrefix, node.id)
    if (res && res.length) {
      const children = res?.map(item => ({
        ...item,
        isLeaf: !item.hasChildren,
      }))

      // 递归找到节点并更新其子项
      const updateNode = (nodes, id) => {
        for (const n of nodes) {
          if (n.id === id) {
            n.children = [...children]
            return
          }
          if (n.children && n.children.length) {
            updateNode(n.children, id)
          }
        }
      }
      updateNode(treeData.value, node.id)
    }
    else {
      // 递归找到节点并更新其isLeaf
      const updateNode = (nodes, id) => {
        for (const n of nodes) {
          if (n.id === id) {
            n.isLeaf = !n.hasChildren
            return
          }
          if (n.children && n.children.length) {
            updateNode(n.children, id)
          }
        }
      }
      updateNode(treeData.value, node.id)
    }
  }
  catch (error) {
    // console.log('error', error)
  }
}

const handleChange = (value, selectedOptions) => {
  emit('updateValue', { value, selectedOptions })
  emit('update:modelValue', { value, selectedOptions })
}

const handleSelect = () => {
  if (!props.questionType && props.formType) {
    message.warning('请先选择问题类型')
  }
}

const initTreeData = async (parentId) => {
  const id = parentId === '业务型' ? 2 : (parentId === '操作型' ? 3 : parentId)
  await getIncidentType(props.apiPrefix, id).then((res) => {
    treeData.value = res?.map(item => ({
      ...item,
      isLeaf: !item.hasChildren,
    }))
  })
}

watch(() => props.questionType, () => {
  if (props.questionType) {
    treeData.value = []
    initTreeData(props.questionType)
  }
  else {
    treeData.value = []
  }
})

onMounted(async () => {
  if (props.formType && props.questionType) {
    await initTreeData(props.questionType)
  }
  else if (!props.formType) {
    await initTreeData(0)
  }
  if (props.bizTypeIds && props.bizTypeIds.length > 0) {
    const selectedOptions = [{ id: 0 }]
    for (const item of props.bizTypeIds) {
      selectedOptions.push({ id: item })
      await onLoadTreeData(selectedOptions)
    }
  }
})
</script>

<template>
  <a-cascader
    style="width: 100%"
    allow-clear
    expand-trigger="click"
    :change-on-select="true"
    :options="treeData"
    :field-names="{ children: 'children', label: 'value', key: 'id', value: 'id', pid: 'parentId' }"
    placeholder="请选择下钻类型"
    :load-data="onLoadTreeData"
    @change="handleChange"
    @click="handleSelect"
  />
</template>
