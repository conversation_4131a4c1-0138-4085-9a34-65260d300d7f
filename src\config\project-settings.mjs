// import { cloneDeep } from 'lodash-es'

export default {
  // 本地测试服务器配置，默认统一通过Castle Platform 代理
  server: () => ({
    host: true,
    port: 8001,
    proxy: {
      '/api': {
        // target: 'http://************:9010',
        target: 'http://**********:810',
        // target: 'http://************:9010',
        changeOrigin: true,
        secure: false,
      },
      // '/api/director': {
      //   target: 'http://**********:10088', // 四川
      //   // target: 'http://**********:10088', // 浙江
      //   // target: 'http://*************:10088', // 本地
      //   changeOrigin: true,
      //   secure: false,
      // },
      // '/templateTest/api/director': {
      //   target: 'http://**********:10088', // 四川
      //   // target: 'http://**********:10088', // 浙江
      //   // target: 'http://*************:10088', // 本地
      //   changeOrigin: true,
      //   rewrite: path => path.replace(/^\/templateTest/, ''),
      //   secure: false,
      // },
      // '/SGCB/api/director': {
      //   target: 'http://**********:10088',
      //   changeOrigin: true,
      //   rewrite: path => path.replace(/^\/SGCB/, ''),
      //   secure: false,
      // },
      '/HXKM': {
        target: 'http://**********:8520',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/HXKM/, ''),
        secure: false,
      },
      // '/chat': {
      //   target: 'http://10.10.7.240:19531',
      //   secure: false,
      // },
      // '/knowledge_base': {
      //   target: 'http://10.10.7.240:7863',
      //   secure: false,
      // },
      // '/LKYD/api/director': {
      //   target: 'http://**********:10088',
      //   changeOrigin: true,
      //   rewrite: path => path.replace(/^\/LKYD/, ''),
      //   secure: false,
      // },
      // '/file': {
      //   // target: 'http://**********:8520',
      //   target: 'http://************:9010',
      //   changeOrigin: true,
      //   secure: false,
      // },
      // '/myvanna': {
      //   target: 'http://************:9010',
      //   changeOrigin: true,
      //   secure: false,
      // },
    },
  }),
  vite: () => {
    const baseConfig = {
      build: {
        rollupOptions: {
          // 如有需要测试，请自行开启
          // plugins: [visualizer({ gzipSize: false })],
        },
        assetsInclude: ['**/*.mp3', '**/*.wav'],
      },
    }
    return {
      ...baseConfig,
    }
  },
}
