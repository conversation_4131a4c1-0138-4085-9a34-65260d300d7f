<route lang="json">
{
  "meta": {
    "title": "粮储GPT",
    "icon": "DesktopOutlined",
    "requiresAuth": false,
    "sort": 1,
    "layout": "blank",
    "hideInMenu": true,
    "hideInBreadcrumb": true
  }
}
</route>

<script setup>
import ChatSDK from './_chat-sdk.js'
import './chat-sdk.css'

// url 行业 GPT 页面地址；
// showMode（展示方式：1 右侧弹出；2 网页）
// apiPrefix（api前缀，string类型）
// isDrag（是否可拖拽，boolean）
// themeName（主题名称，'SGCB'收购储备; 'HZTJ'火灾统计）
new ChatSDK({
  showMode: 1,
  url: '/home',
  apiPrefix: 'SGCB',
  isDrag: true,
  themeName: 'SGCB',
  robotImageUrl: '/images/robot-1.gif',
  userImageUrl: '/images/user.png',
  // customParams: {
  //   AIName: '智粮助手',
  //   AIDescription: '作为一名深耕粮食行业的AI机器人，我可以陪您聊天，为您提供整理数据、统计分析等智能工作，希望能为您分担工作中的难题。现在我还是一名初出茅庐的助理，在持续升级和进步，请多多关注！',
  //   AISidebarDesc: '粮食信息的AI助手',
  // },
}).init()
</script>

<template>
  <div class="h-screen bg-slate-600 p-72">
    <h1 class="text-center text-gray-100">
      其他内容
    </h1>
  </div>
</template>

<style lang="less" scoped>

</style>
