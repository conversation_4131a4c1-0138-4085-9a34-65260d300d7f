{"name": "hxdi-party-building-ai", "version": "0.0.11", "description": "党建GPT", "author": "ouyangshun", "license": "ISC", "main": "src/pages/sdk/_chat-sdk.js", "files": ["src/pages/sdk/_chat-sdk.js", "src/pages/sdk/chat-sdk.css"], "engines": {"node": ">=14.18.0"}, "scripts": {"dev": "castle dev", "build": "castle build", "optimize": "castle optimize", "preview": "castle preview --port 4173", "lint-staged": "npx lint-staged", "prepare": "husky install", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@castle/ant-design-vue": "latest", "@castle/business-components": "^0.2.16", "@castle/castle-template": "^2.1.31", "@castle/pro-layout": "latest", "@microsoft/fetch-event-source": "^2.0.1", "artyom.js": "^1.0.6", "dayjs": "^1.11.10", "howler": "^2.2.4", "lru-cache": "^6.0.0", "plotly.js-dist": "^3.0.1", "uuid": "^11.1.0", "viewerjs": "^1.11.6", "vue-demi": "^0.14.8", "vue-markdown-render": "^2.1.1", "vue-native-websocket": "^2.0.15"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "eslint-plugin-check-file": "2.6.2", "eslint-plugin-vue-scoped-css": "^2.5.0", "husky": "^8.0.2", "lint-staged": "^13.1.0", "vue-eslint-parser": "^9.3.1"}, "lint-staged": {"*.{vue,js,jsx,cjs,mjs}": ["eslint --ignore-path .gitignore"]}}